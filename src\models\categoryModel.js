const { pool } = require('../config/db');

class Category {
  // 创建新分类
  static async create(categoryData) {
    try {
      const [result] = await pool.query(
        `INSERT INTO categories (category, content) VALUES (?, ?)`,
        [categoryData.category, categoryData.content]
      );
      return result.insertId;
    } catch (error) {
      console.error('创建分类失败:', error);
      throw error;
    }
  }

  // 获取所有分类
  static async findAll() {
    try {
      const [rows] = await pool.query('SELECT * FROM categories ORDER BY created_at DESC');
      return rows;
    } catch (error) {
      console.error('获取分类列表失败:', error);
      throw error;
    }
  }

  // 根据ID获取分类
  static async findById(id) {
    try {
      const [rows] = await pool.query('SELECT * FROM categories WHERE id = ?', [id]);
      return rows[0];
    } catch (error) {
      console.error('获取分类信息失败:', error);
      throw error;
    }
  }

  // 更新分类信息
  static async update(id, categoryData) {
    try {
      const [result] = await pool.query(
        `UPDATE categories SET category = ?, content = ? WHERE id = ?`,
        [categoryData.category, categoryData.content, id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新分类信息失败:', error);
      throw error;
    }
  }

  // 删除分类
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM categories WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除分类失败:', error);
      throw error;
    }
  }

  // 搜索分类
  static async search(keyword) {
    try {
      const [rows] = await pool.query(
        `SELECT * FROM categories WHERE category LIKE ? OR content LIKE ? ORDER BY created_at DESC`,
        [`%${keyword}%`, `%${keyword}%`]
      );
      return rows;
    } catch (error) {
      console.error('搜索分类失败:', error);
      throw error;
    }
  }
}

module.exports = Category;