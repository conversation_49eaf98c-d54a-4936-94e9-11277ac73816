<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, shrink-to-fit=no"
    />
    <title>乐高人仔定制</title>
    <!-- 引入Fabric.js库 -->
    <script src="../js/fabric.js"></script>

    <link
      href="../js/bootstrap-5.3.7-dist/bootstrap-5.3.7-dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="../styles.css" />
    <link href="../js/bootstrap-icons-1.13.1/icon.css" rel="stylesheet" />
    <script src="../js/bootstrap-5.3.7-dist/bootstrap-5.3.7-dist/js/bootstrap.bundle.min.js"></script>
    <script src="../js/vue.js"></script>
    <script src="../js/dist2/axios.min.js"></script>
    <script src="../js/api.js"></script>
  </head>

  <body>
    <!-- 头部导航栏 -->
    <div class="header">
      <div class="d-flex justify-content-between align-items-center">
        <h1 class="header-title">好有趣@乐高人仔定制</h1>
        <button class="menu-button" id="menuToggle" type="button">
          <i class="bi bi-list"></i>
        </button>
        <nav class="nav-menu" id="navMenu">
          <ul>
            <li><a href="#welcome-section">首页</a></li>
            <li><a href="#preview-section">3D预览</a></li>
            <li><a href="#customize-section">你的定制</a></li>
            <li><a href="#faq-section">常见问题</a></li>
          </ul>
        </nav>
      </div>
    </div>

    <div class="main-container" id="app">
      <!-- 第一板块：欢迎页面 - 不使用卡片包裹 -->
      <div id="welcome-section" class="welcome-section">
        <div class="welcome-card">
          <div>
            <h2 class="welcome-title">创造你的专属<br />乐高®人仔</h2>
            <p class="welcome-description">
              从发型到服饰，从表情到配饰，完全按照你的想好定制独一无二的乐高人仔。
            </p>
          </div>
          <div class="welcome-image-container">
            <!-- <img src="https://via.placeholder.com/350x200/333333/ffffff?text=乐高人仔示例图"
                        class="welcome-image-large" alt="乐高人仔"> -->
          </div>
        </div>
      </div>

      <div class="divider">
        <span>乐高预览</span>
      </div>

      <div class="section-card">
        <div class="card-header customize-header">
          <h5 class="m-0">预览页面</h5>
        </div>
        <div class="card-body">
          <div id="preview-section">
            <div class="main-figure-flex figure-transition active">
              <div class="sidebar">
                <div class="tab-buttons">
                  <div class="tab-button active" data-tab-target="#tab-head">
                    <i class="bi bi-house"></i>
                    <div>头部</div>
                  </div>
                  <div class="tab-button" data-tab-target="#tab-hair">
                    <i class="bi bi-magic"></i>
                    <div>发型</div>
                  </div>
                  <div
                    class="tab-button qg"
                    data-tab-target="#tab-expression"
                    @click="toQg"
                  >
                    <i class="bi bi-emoji-smile"></i>
                    <div>躯干</div>
                  </div>
                  <div class="tab-button" data-tab-target="#tab-color">
                    <i class="bi bi-body-text"></i>
                    <div>腿部</div>
                  </div>
                  <div class="tab-button" data-tab-target="#tab-details">
                    <i class="bi bi-tools"></i>
                    <div>配件</div>
                  </div>
                </div>

                <div class="tab-content active" id="tab-head">
                  <div class="style-section">
                    <div v-if="headCategory.length === 0" class="empty-state">
                      <i class="bi bi-box"></i>
                      <p>暂无内容</p>
                    </div>
                    <div
                      v-for="(category1, index1) in headCategory"
                      :key="category1.name"
                      class="category"
                    >
                 
                      <div class="category-title">{{ category1.name }}</div>
                      <div
                        v-if="category1.list.length === 0"
                        class="empty-category"
                      >
                        <i class="bi bi-inbox"></i>
                        <span>暂无内容</span>
                      </div>
                      <div v-else class="category-grid">
                     
                        <div
                          v-for="(item1, itemIndex1) in category1.list"
                          :key="`head-${item1.id}`"
                        
                          :class="{ selected: item1.check, 'head-item': true }"
                          @click="selectItem('head', item1.id, index1, itemIndex1, item1)"
                        >
                          
                          <img :src="item1.src" :alt="item1.alt" />
                          <div
                            v-if="item1.check"
                            class="head-check"
                          >
                            <i class="bi bi-check"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="tab-content" id="tab-hair">
                  <div class="hairstyle-section">
                    <div v-if="hairCategory.length === 0" class="empty-state">
                      <i class="bi bi-scissors"></i>
                      <p>暂无内容</p>
                    </div>
                    <div
                      v-for="(category2, index2) in hairCategory"
                      :key="category2.name"
                      class="category"
                    >
                      <div class="category-title">{{ category2.name }}</div>
                      <div
                        v-if="category2.list.length === 0"
                        class="empty-category"
                      >
                        <i class="bi bi-inbox"></i>
                        <span>暂无内容</span>
                      </div>
                      <div v-else class="category-grid">
                        <!-- 发型 -->
                        <div
                          v-for="(item2, itemIndex2) in category2.list"
                          :key="`hair-${item2.id}`"
                         
                          :class="{ selected: item2.check, 'hair-item': true }"
                          @click="selectItem('hair', item2.id, index2, itemIndex2, item2)"
                        >
                          <img
                            :src="item2.src"
                            :alt="itemIndex2.alt"
                            :data-hair-class="item2.dataClass"
                          />
                          <div
                            v-if="item2.check"
                            class="hair-check"
                          >
                            <i class="bi bi-check"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="tab-content" id="tab-color">
                  <div class="style-section">
                    <div v-if="legCategory.length === 0" class="empty-state">
                      <i class="bi bi-person-standing"></i>
                      <p>暂无内容</p>
                    </div>
                    <div
                      v-for="(category3, index3) in legCategory"
                      :key="category3.name"
                      class="category"
                    >
                      <div class="category-title">{{ category3.name }}</div>
                      <div
                        v-if="category3.list.length === 0"
                        class="empty-category"
                      >
                        <i class="bi bi-inbox"></i>
                        <span>暂无内容</span>
                      </div>
                      <div v-else class="category-grid">
                        <!-- 腿部 -->
                        <div
                          v-for="(item3, itemIndex3) in category3.list"
                          :key="`leg-${item3.id}`"
                        
                          :class="{ selected: item3.check, 'expression-item': true }"
                          @click="selectItem('leg', item3.id, index3, itemIndex3, item3)"
                        >
                          <img :src="item3.src" :alt="item3.alt" />
                          <div
                            v-if="item3.check"
                            class="expression-check"
                          >
                            <i class="bi bi-check"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="tab-content" id="tab-details">
                  <div class="style-section">
                    <div
                      v-if="detailsCategory.length === 0"
                      class="empty-state"
                    >
                      <i class="bi bi-tools"></i>
                      <p>暂无内容</p>
                    </div>
                    <div
                      v-for="(category4, index4) in detailsCategory"
                      :key="category4.name"
                      class="category"
                    >
                      <div class="category-title">{{ category4.name }}</div>
                      <div
                        v-if="category4.list.length === 0"
                        class="empty-category"
                      >
                        <i class="bi bi-inbox"></i>
                        <span>暂无内容</span>
                      </div>
                      <div v-else class="category-grid">
                        <!-- 配件 -->
                        <div
                          v-for="(item4, itemIndex4) in category4.list"
                          :key="`details-${item4.id}`"
                     
                          :class="{ selected: item4.check, 'details-item': true }"
                          @click="selectItem('details', item4.id, index4, itemIndex4, item4)"
                        >
                          <img :src="item4.src" :alt="item4.alt" />
                          <div
                            v-if="item4.check"
                            class="details-check"
                          >
                            <i class="bi bi-check"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="preview">
                <div class="lego-figure">
                  <div class="control-buttons">
                    <button
                      class="btn btn-outline-primary btn-sm"
                      id="flipButton"
                      @click="flipFigure"
                    >
                      <i class="bi bi-arrow-repeat"></i>
                    </button>
                  </div>
                  <div
                    class="lego-figure-display"
                    style="width: 100%; height: 100%"
                  >
                    <div
                      class="lego-figure-ca"
                      data-test="display-front-headwear"
                      style="z-index: 6"
                    >
                      <div
                        class="lego-figure-ca2"
                        style="
                          transform: translateY(0px) scale(1.01074)
                            translateZ(0px);
                        "
                      >
                        <img
                          data-test="lego-figure-ca3 hair"
                          src="https://www.lego.com/cdn/mff/display/4568933_0.png"
                          class="lego-figure-ca4"
                        />
                      </div>
                    </div>

                    <div
                      class="lego-figure-ca"
                      data-test="minifigure-display-front-head"
                      style="z-index: 5"
                    >
                      <div
                        class="lego-figure-ca2"
                        style="
                          transform: translateY(0px) scale(1.01074)
                            translateZ(0px);
                        "
                      >
                        <img
                          alt="Minifigure"
                          data-test="lego-figure-ca3 head"
                          src="https://www.lego.com/cdn/mff/display/6153335_0.png"
                          class="lego-figure-ca4"
                        />
                      </div>
                    </div>

                    <div
                      class="lego-figure-ca"
                      data-test="minifigure-display-front-torso"
                      style="z-index: 4"
                    >
                      <div
                        class="lego-figure-ca2"
                        style="
                          transform: translateY(0px) scale(1.01074)
                            translateZ(0px);
                        "
                      >
                        <img
                          alt="Minifigure"
                          data-test="lego-figure-ca3 expression"
                          src="https://www.lego.com/cdn/mff/display/4275461_0.png"
                          class="lego-figure-ca4"
                        />
                        <img
                          data-test="minifigure-overlay-image qg"
                          alt=""
                          class="MinifigureBrick_overlay__zBY1_ MinifigureBrick_cropped__9s96E"
                        
                          style="z-index: 5"
                        />
                      </div>
                    </div>
                    <div
                      class="lego-figure-ca"
                      data-test="minifigure-display-front-leg"
                      style="z-index: 3"
                    >
                      <div
                        class="lego-figure-ca2"
                        style="
                          transform: translateY(0px) scale(1.01074)
                            translateZ(0px);
                        "
                      >
                        <img
                          alt="Minifigure"
                          data-test="lego-figure-ca3 leg"
                          src="https://www.lego.com/cdn/mff/display/9341_0.png"
                          class="lego-figure-ca4"
                        />
                      </div>
                    </div>
                    <div
                      class="lego-figure-ca"
                      data-test="minifigure-display-front-acc"
                      style="z-index: 7"
                    >
                      <div class="lego-figure-ca2" style="transform: none">
                        <img
                          alt="Minifigure"
                          data-test="lego-figure-ca3 details"
                          src="https://www.lego.com/cdn/mff/display/4600307_0.png"
                          class="lego-figure-ca4"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="main-figure-flex-v2 figure-transition">
              <div class="modal-content">
                <section class="modal-content-padding">
                  <div class="modal-container">
                    <div data-test="customer" class="customer-container">
                      <div class="customer-container-wrapper">
                        <!-- Tab Navigation -->
                        <div class="torso-tab-nav">
                          <div 
                            class="torso-tab-btn" 
                            :class="{ active: activeTorsoTab === 'clothing' }"
                            @click="switchTorsoTab('clothing')"
                          >
                            <i class="bi bi-person"></i>
                            <span>服装</span>
                          </div>
                          <div 
                            class="torso-tab-btn" 
                            :class="{ active: activeTorsoTab === 'decorations' }"
                            @click="switchTorsoTab('decorations')"
                          >
                            <i class="bi bi-heart"></i>
                            <span>装饰</span>
                          </div>
                          <div 
                            class="torso-tab-btn" 
                            :class="{ active: activeTorsoTab === 'text' }"
                            @click="switchTorsoTab('text')"
                          >
                            <i class="bi bi-fonts"></i>
                            <span>文字</span>
                          </div>
                        </div>

                        <!-- Tab Content -->
                        <div class="torso-tab-content">
                          <!-- 服装选择 -->
                          <div v-show="activeTorsoTab === 'clothing'" class="style-section">
                            <div v-if="clothingCategory.length === 0" class="empty-state">
                              <i class="bi bi-person"></i>
                              <p>暂无内容</p>
                            </div>
                            <div
                              v-for="(category5, index5) in clothingCategory"
                              :key="category5.name"
                              class="category"
                            >
                              <div class="category-title">{{ category5.name }}</div>
                              <div
                                v-if="category5.list.length === 0"
                                class="empty-category"
                              >
                                <i class="bi bi-inbox"></i>
                                <span>暂无内容</span>
                              </div>
                              <div v-else class="category-grid">
                                <div
                                  v-for="(item5, itemIndex5) in category5.list"
                                  :key="`clothing-${item5.id}`"
                                
                                  :class="{ selected: item5.check, 'clothing-item': true }"
                                  @click="selectItem('clothing', item5.id, index5, itemIndex5, item5)"
                                >
                                  <img :src="item5.src" :alt="item5.alt" />
                                  <div
                                    v-if="item5.check"
                                    class="clothing-check"
                                  >
                                    <i class="bi bi-check"></i>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 装饰选择 -->
                          <div v-show="activeTorsoTab === 'decorations'" class="style-section">
                            <div v-if="decorationCategory.length === 0" class="empty-state">
                              <i class="bi bi-heart"></i>
                              <p>暂无内容</p>
                            </div>
                            <div
                              v-for="(category6, index6) in decorationCategory"
                              :key="category6.name"
                              class="category"
                            >
                              <div class="category-title">{{ category6.name }}</div>
                              <div
                                v-if="category6.list.length === 0"
                                class="empty-category"
                              >
                                <i class="bi bi-inbox"></i>
                                <span>暂无内容</span>
                              </div>
                              <div v-else class="category-grid">
                                <div
                                  v-for="(item6, itemIndex6) in category6.list"
                                  :key="`decoration-${item6.id}`"
                                
                                   :class="{ selected: item6.check, 'decoration-item': true }"
                                  @click="selectDecoration('decoration', item6.id, index6, itemIndex6, item6)"
                                >
                                  <img :src="item6.src" :alt="item6.alt" />
                                  <div
                                    v-if="item6.check"
                                    class="decoration-check"
                                  >
                                    <i class="bi bi-check"></i>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- 文字工具 -->
                          <div v-show="activeTorsoTab === 'text'" class="style-section">
                            <div class="category">
                              <div class="category-title">文字工具</div>
                              <div class="text-controls">
                                <div class="input-group">
                                  <input 
                                    type="text" 
                                    v-model="customText" 
                                    placeholder="输入文字内容" 
                                    class="form-control"
                                    @keyup.enter="addText"
                                  >
                                  <button @click="addText" class="btn btn-primary">
                                    <i class="bi bi-plus"></i>
                                  </button>
                                </div>
                                
                                <div class="control-row color-row">
                                  <label>颜色:</label>
                                  <input type="color" v-model="textColor" @change="updateTextColor">
                                </div>
                                
                                <div class="control-row font-row">
                                  <label>字体:</label>
                                  <select v-model="fontFamily" @change="updateFontFamily" class="form-select">
                                    <option value="Arial">Arial</option>
                                    <option value="Times New Roman">Times</option>
                                    <option value="Helvetica">Helvetica</option>
                                    <option value="Georgia">Georgia</option>
                                    <option value="Verdana">Verdana</option>
                                  </select>
                                </div>
                                
                                <div class="control-row size-row">
                                  <label>大小:</label>
                                  <input 
                                    type="range" 
                                    v-model="textSize" 
                                    min="12" 
                                    max="72" 
                                    @input="updateTextSize"
                                  >
                                  <span>{{ textSize }}</span>px
                                </div>
                                
                              
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="customer-container-wrapper2">
                       
                        
                        <!-- 重置和确认按钮 -->
                        <div class="torso-action-buttons">
                          <button
                            class="btn btn-outline-secondary btn-sm back-btn"
                            @click="backToMinifigure"
                          >
                            <i class="bi bi-arrow-left"></i>
                          </button>
                          <button 
                            class="btn btn-outline-danger btn-sm back-btn"
                            @click="resetTorsoCompletely"
                            title="重置躯干"
                          >
                            <i class="bi bi-arrow-clockwise"></i>
                          </button>
                          
                          <button 
                            class="btn btn-primary btn-sm back-btn"
                            @click="filpConfirm"
                            title="翻转"
                          >
                            <i class="bi bi-check-lg"></i>
                          </button>
                        </div>
                        
                        <!-- Canvas区域 -->
                        <div class="CustomiseTorso_konvaWrapper__w_9lQ">
                          <div id="mff-entry">
                            <div
                              class="konvajs-content"
                              role="presentation"
                              style="position: relative; user-select: none"
                            >
                              <canvas
                                id="torsoCanvas"
                                style="
                                  padding: 0px;
                                  margin: 0px;
                                  border: 0px;
                                  background: transparent;
                                  position: absolute;
                                  top: 0px;
                                  left: 0px;
                                  width: 0px;
                                  height: 0px;
                                  display: block;
                                "
                              ></canvas>
                              <!-- 正面Canvas -->
                              <canvas
                              v-show="!filpFlase"
                                id="canvas2"
                                style="
                                  padding: 0px;
                                  margin: 0px;
                                  border: 0px;
                                  background: transparent;
                                  position: absolute;
                                  top: 0px;
                                  left: 0px;
                                  width: 0px;
                                  height: 0px;
                                  display: block;
                                "
                              ></canvas>
                              <!-- 背面Canvas -->
                              <canvas
                              v-show="filpFlase"
                                id="canvas2Back"
                                style="
                                  padding: 0px;
                                  margin: 0px;
                                  border: 0px;
                                  background: transparent;
                                  position: absolute;
                                  top: 0px;
                                  left: 0px;
                                  width: 0px;
                                  height: 0px;
                                  display: none;
                                "
                              ></canvas>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="divider">
        <span>定制选项</span>
      </div>

      <!-- 第三板块：自定义页面 -->
      <div class="section-card">
        <div class="card-header customize-header">
          <h5 class="m-0">你的定制</h5>
        </div>
        <div class="card-body" style="box-sizing: border; padding: 10px;">
          <div id="customize-section">
            <div class="customize-content">
              

              <div class="section-title mt-4">
                <h6>已选部件</h6>
              </div>

              <!-- 动态显示已选部件 -->
              <div v-if="getSelectedParts().length === 0" class="empty-parts">
                <i class="bi bi-inbox"></i>
                <span>暂无选择的部件</span>
              </div>

              <div v-for="part in getSelectedParts()" :key="part.type" class="part-item">
                <div class="d-flex align-items-center">
                  <div class="part-icon me-3" :style="part.iconStyle">
                    <i :class="part.icon" v-if="part.icon"></i>
                    <img v-if="part.thumbnail" :src="part.thumbnail" alt="" style="width: 100%; height: 100%; object-fit: cover; border-radius: 4px;">
                  </div>
                  <div>{{ part.name }}</div>
                </div>
                <i class="bi bi-x" @click="removePart(part.type)" style="cursor: pointer;"></i>
              </div>

              <div class="section-title mt-4">
                <h6>购买数量</h6>
              </div>

              <div class="quantity-control justify-content-center">
                <button class="btn btn-outline-secondary btn-circle" @click="decreaseQuantity">
                  <i class="bi bi-dash"></i>
                </button>
                <span class="fw-bold">{{ purchaseQuantity }}</span>
                <button class="btn btn-outline-secondary btn-circle" @click="increaseQuantity">
                  <i class="bi bi-plus"></i>
                </button>
              </div>

              <div class="d-grid gap-2 mt-4">
                <button class="btn btn-danger" @click="confirmOrder">
                  <i class="bi bi-cart"></i> 确认设计
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="divider">
        <span>帮助中心</span>
      </div>

      <!-- 第四板块：FAQ页面 -->
      <div class="section-card">
        <div class="card-body"   style="box-sizing: border; padding: 10px;">
          <div id="faq-section">
            <div class="button-group mb-4"></div>

            <div class="faq-list">
              <div class="faq-item">
                <div class="faq-question">
                  <i class="bi bi-question-circle"></i>
                  <div>定制的乐高小人需要多长时间才能送达？</div>
                </div>
                <div class="faq-answer">
                  一般情况下，您收到订单确认后3-5个工作日，我们会将您定制的乐高小人发出。具体时间取决于您所在地区，最长将在10-15个工作日内送达。
                </div>
              </div>

              <div class="faq-item">
                <div class="faq-question">
                  <i class="bi bi-question-circle"></i>
                  <div>可以修改已经提交的订单吗？</div>
                </div>
                <div class="faq-answer">
                  在订单处理阶段（通常下单后24小时内），您可以联系客服修改设计。一旦开始生产，将无法进行修改。
                </div>
              </div>

              <div class="faq-item">
                <div class="faq-question">
                  <i class="bi bi-question-circle"></i>
                  <div>定制的乐高小人可以退货吗？</div>
                </div>
                <div class="faq-answer">
                  由于定制产品的特性，除产品本身存在质量问题外，定制的乐高小人不支持退换货。请在下单前仔细确认您的设计。
                </div>
              </div>

              <div class="faq-item">
                <div class="faq-question">
                  <i class="bi bi-question-circle"></i>
                  <div>可以使用自己的照片来制作乐高小人的面部吗？</div>
                </div>
                <div class="faq-answer">
                  目前我们不支持使用用户提供的图片制作面部表情，但提供多种预设表情供您选择。
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

          <div class="modal fade" id="orderModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">订单信息</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form id="orderForm">
              <div class="mb-3">
                <label class="form-label">姓名 *</label>
                <input type="text" class="form-control" v-model="orderInfo.name" required>
              </div>
              <div class="mb-3">
                <label class="form-label">电话 *</label>
                <input type="tel" class="form-control" v-model="orderInfo.phone" required>
              </div>
              <div class="mb-3">
                <label class="form-label">邮箱 *</label>
                <input type="email" class="form-control" v-model="orderInfo.email" required>
              </div>
             
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-danger" @click="submitOrder">提交订单</button>
          </div>
        </div>
      </div>
    </div>
    </div>

    <!-- Bootstrap确认模态框 -->
    <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="confirmModalLabel">确认操作</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body" id="confirmModalBody">
            确认要执行此操作吗？
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            <button type="button" class="btn btn-primary" id="confirmModalConfirm">确认</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading模态框 -->
    <div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
      <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">
          <div class="modal-body text-center py-4">
            <div class="spinner-border text-primary mb-3" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
            <div id="loadingText">正在处理中...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 订单信息填写模态框 -->


    <script>
      window.onload = () => {
        loadTool3D();
        // 检测设备类型，添加设备特定的类
        function checkDeviceType() {
          const isMobile = window.innerWidth <= 768;
          const isPc = window.innerWidth >= 992;

          document.body.classList.remove("is-mobile", "is-pc");

          if (isMobile) {
            document.body.classList.add("is-mobile");
          } else if (isPc) {
            document.body.classList.add("is-pc");
          }
        }

        // 初始检查设备类型
        checkDeviceType();

        // 窗口大小变化时重新检查
        window.addEventListener("resize", checkDeviceType);

        // 菜单切换功能
        const menuButton = document.getElementById("menuToggle");
        const navMenu = document.getElementById("navMenu");

        if (menuButton && navMenu) {
          menuButton.addEventListener("click", function () {
            navMenu.classList.toggle("active");
          });

          // 点击菜单项后关闭菜单
          const menuLinks = navMenu.querySelectorAll("a");
          menuLinks.forEach((link) => {
            link.addEventListener("click", function () {
              if (window.innerWidth < 992) {
                // 只在移动端关闭菜单
                navMenu.classList.remove("active");
              }
            });
          });
        }
      };

      function loadTool3D() {
        const tabButtons = document.querySelectorAll(".tab-button");
        const tabContents = document.querySelectorAll(".tab-content");
        console.log(tabButtons, 'tabButtons..')
        tabButtons.forEach((button) => {
          button.addEventListener("click", function (event) {
            console.log(event, 'event...')
            // 躯干逻辑
            if (this.classList[1] == "qg") {
              document
                .querySelector(".main-figure-flex")
                .classList.remove("active");
              document
                .querySelector(".main-figure-flex-v2")
                .classList.add("active");

              // 延迟执行Canvas初始化，确保DOM元素已显示
              //setTimeout(() => {
              //  this.initTorsoCanvas();
              //}, 100);
            } else {
              // 使用动画类切换显示
              document
                .querySelector(".main-figure-flex")
                .classList.add("active");
              document
                .querySelector(".main-figure-flex-v2")
                .classList.remove("active");
            }

            // 移除所有按钮的active类
            tabButtons.forEach((btn) => btn.classList.remove("active"));
            // 添加当前按钮的active类
            this.classList.add("active");

            // 处理标签内容显示/隐藏
            const targetTab = this.getAttribute("data-tab-target");
            if (targetTab) {
              // 隐藏所有标签内容
              tabContents.forEach((content) =>
                content.classList.remove("active")
              );
              // 显示目标标签内容
              const targetContent = document.querySelector(targetTab);
              if (targetContent) {
                targetContent.classList.add("active");
              }
            }
          });
        });
      }
    </script>
    <script>
      new Vue({
        el: "#app",
        data: {
          img: null,
          canvasContainer: null,
          backgroundGg: {
            default: "./AA_Plain_Torso01M_front.png",
            plain_white: "./AA_Plain_Torso01M_front.png",
            plain_red:
              "https://www.lego.com/cdn/cs/set/assets/bltf6cf85cbe9964812/castle_03_front.png", // 暂时使用默认图片，您可以替换为真实的红色躯干图片
            plain_blue: "./AA_Plain_Torso01M_front.png", // 暂时使用默认图片，您可以替换为真实的蓝色躯干图片
            plain_black: "./AA_Plain_Torso01M_front.png", // 暂时使用默认图片，您可以替换为真实的黑色躯干图片
            striped: "./AA_Plain_Torso01M_front.png", // 暂时使用默认图片，您可以替换为真实的条纹躯干图片
            logo: "./AA_Plain_Torso01M_front.png", // 暂时使用默认图片，您可以替换为真实的LOGO躯干图片
          },
          // 背面背景图片配置
          backgroundGgBack: {
            default: "./AA_Plain_Torso01M_back.png",
            plain_white: "./AA_Plain_Torso01M_back.png",
            plain_red: "./AA_Plain_Torso01M_back.png", // 背面红色躯干图片
            plain_blue: "./AA_Plain_Torso01M_back.png", // 背面蓝色躯干图片
            plain_black: "./AA_Plain_Torso01M_back.png", // 背面黑色躯干图片
            striped: "./AA_Plain_Torso01M_back.png", // 背面条纹躯干图片
            logo: "./AA_Plain_Torso01M_back.png", // 背面LOGO躯干图片
          },
          currentBackgroundType: "https://www.lego.com/cdn/cs/set/assets/blt2eadcec57f47be3c/AA_Plain_Torso01M_front.png",
          currentBackVgroundType: 'https://www.lego.com/cdn/cs/set/assets/blt62d836036e019179/AA_Plain_Torso01M_back.png',
          containerWidth: null,
          containerHeight: null,
          fabricCanvas2: null,
          backgroundImage2: null,
          // 背面Canvas相关变量
          fabricCanvas2Back: null,
          backgroundImage2Back: null,
          message: "Hello Vue!",
          currentType: null,
          head: null,
          hair: null,
          expression: null,
          leg: null,
          details: null,
          clothing: null,
          decoration: null,
          selectedItems: {
            head: null,
            hair: null,
            expression: null,
            leg: null,
            details: null,
            clothing: null,  // 新增
            decoration: null, // 新增
            torso: null
          },
          filp: false,
          filpFlase: false,
          headCategory: [
            {
              name: "头部",
              list: [
                {
                  id: 1,
                  src: "https://www.lego.com/cdn/mff/thumbnails/6419092_0.png",
                  src2: "https://www.lego.com/cdn/mff/display/6419092_0.png",
                  alt: "头部1",
                },
                {
                  id: 2,
                  src: "https://www.lego.com/cdn/mff/thumbnails/6112622_0.png",
                  src2: "https://www.lego.com/cdn/mff/display/6112622_0.png",
                  alt: "头部2",
                },
              ],
            },
          ],
          hairCategory: [
            {
              name: "黑色",
              list: [
                {
                  id: 1,
                  src: "https://www.lego.com/cdn/mff/thumbnails/4653226_0.png",
                  src2: "https://www.lego.com/cdn/mff/display/4653226_0.png",
                  alt: "黑色发型1",
                  dataClass: "black-hair-1",
                },
                {
                  id: 2,
                  src: "https://www.lego.com/cdn/mff/thumbnails/6409770_0.png",
                  src2: "https://www.lego.com/cdn/mff/display/6409770_0.png",
                  alt: "黑色发型2",
                  dataClass: "black-hair-2",
                },
              ],
            },
            {
              name: "金色",
              list: [],
            },
          ],
          legCategory: [
            {
              name: "大腿",
              list: [
                {
                  id: 1,
                  src: "https://www.lego.com/cdn/mff/thumbnails/6270457_0.png",
                  src2: "https://www.lego.com/cdn/mff/display/6270457_0.png",
                  alt: "腿部1",
                },
                {
                  id: 2,
                  src: "https://www.lego.com/cdn/mff/thumbnails/4221886_0.png",
                  src2: "https://www.lego.com/cdn/mff/display/4221886_0.png",
                  alt: "腿部2",
                },
              ],
            },
          ],
          detailsCategory: [
            {
              name: "配件",
              list: [
                {
                  id: 1,
                  src: "https://www.lego.com/cdn/mff/thumbnails/6379176_0.png",
                  src2: "https://www.lego.com/cdn/mff/display/6379176_0.png",
                  alt: "配件1",
                },
              ],
            },
          ],
          activeTorsoTab: 'clothing',
          customText: '',
          textColor: '#000000',
          textSize: 24,
          fontFamily: 'Arial',
          selectedClothing: null,
          selectedDecoration: null,
          // 新增服装分类数据
          clothingCategory: [
            {
              name: "服装选择",
              list: [
                {
                  id: 1,
                  src: "https://www.lego.com/cdn/cs/set/assets/blte50185b986e7127b/castle_01_front.png?format=webply&fit=bounds&quality=55&width=50&height=50&dpr=3",
                  src2: "https://www.lego.com/cdn/cs/set/assets/blte50185b986e7127b/castle_01_front.png"
                },
                {
                  id: 2,
                  src: "https://www.lego.com/cdn/cs/set/assets/blt671d001d3675ab3a/castle_02_front.png?format=webply&fit=bounds&quality=55&width=50&height=50&dpr=3", 
                src2: "https://www.lego.com/cdn/cs/set/assets/blt671d001d3675ab3a/castle_02_front.png"
                }
              ]
            }
          ],
          // 新增装饰分类数据
          decorationCategory: [
            {
              name: "装饰元素",
              list: [
                {
                  id: 1,
                  src: "https://www.lego.com/cdn/cs/set/assets/blt99775203b311317f/02_Sticker_TRANSFORMERS_LOGO_1.png?format=webply&fit=bounds&quality=55&width=50&height=50&dpr=3",
                  src2: "https://www.lego.com/cdn/cs/set/assets/blt99775203b311317f/02_Sticker_TRANSFORMERS_LOGO_1.png",
                  alt: "星星"
                }
              ]
            }
          ],
          showMinifigure: true, // 确保初始显示人仔界面
          showTorso: false,     // 初始不显示躯干界面
          torsoInitialized: false, // 标记躯干是否已初始化
          // 添加正反面base64存储变量
          torsoFrontBase64: null,
          torsoBackBase64: null,
          // 人仔当前显示的面（正面/背面）
          minifigureFlipped: false,
          purchaseQuantity: 1, // 添加购买数量
          orderInfo: {
            name: '',
            phone: '',
            email: ''
          }
        },
        methods: {
           increaseQuantity() {
    if (this.purchaseQuantity < 99) {
      this.purchaseQuantity++;
    }
  },
  
  decreaseQuantity() {
    if (this.purchaseQuantity > 1) {
      this.purchaseQuantity--;
    }
  },
  
  confirmOrder() {
    // 检查是否有选择的部件
    const selectedParts = this.getSelectedParts();
    if (selectedParts.length === 0) {
      this.showNotification('请先选择人仔部件', 'warning');
      return;
    }
    
    // 显示订单信息填写模态框
    const modal = new bootstrap.Modal(document.getElementById('orderModal'));
    modal.show();
  },
  async submitOrder() {
    // 验证表单
    if (!this.orderInfo.name.trim()) {
      this.showNotification('请填写姓名', 'warning');
      return;
    }
    
    if (!this.orderInfo.phone.trim()) {
      this.showNotification('请填写电话', 'warning');
      return;
    }
    
    if (!this.orderInfo.email.trim()) {
      this.showNotification('请填写邮箱', 'warning');
      return;
    }
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this.orderInfo.email)) {
      this.showNotification('请填写正确的邮箱格式', 'warning');
      return;
    }
    
    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(this.orderInfo.phone)) {
      this.showNotification('请填写正确的手机号', 'warning');
      return;
    }
    
    // 显示提交中状态
    const loading = this.showLoading('正在提交订单...');
    
    try {
      // 模拟提交延迟
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 构建订单数据
      const orderData = {
        customerInfo: {
          name: this.orderInfo.name,
          phone: this.orderInfo.phone,
          email: this.orderInfo.email
        },
        quantity: this.purchaseQuantity,
        selectedParts: this.getSelectedParts(),
        torsoDesign: this.torsoFrontBase64,
        orderTime: new Date().toISOString(),
        orderId: 'LEGO' + Date.now()
      };
      
      console.log('订单数据:', orderData);
      
      // 关闭模态框
      const modal = bootstrap.Modal.getInstance(document.getElementById('orderModal'));
      modal.hide();
      
      // 显示成功消息
      this.hideLoading();
      this.showNotification('订单提交成功！订单号：' + orderData.orderId, 'success');
      
      // 重置表单
      this.orderInfo = {
        name: '',
        phone: '',
        email: ''
      };
      
    } catch (error) {
      this.hideLoading();
      console.error('提交订单失败:', error);
      this.showNotification('提交失败，请重试', 'error');
    }
  },
           showBootstrapConfirm(message, title = '确认操作') {
  return new Promise((resolve) => {
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    const modalTitle = document.getElementById('confirmModalLabel');
    const modalBody = document.getElementById('confirmModalBody');
    const confirmBtn = document.getElementById('confirmModalConfirm');
    
    modalTitle.textContent = title;
    modalBody.innerHTML = message.replace(/\n/g, '<br>');
    
    // 移除之前的事件监听器
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    
    // 添加新的事件监听器
    newConfirmBtn.addEventListener('click', () => {
      modal.hide();
      resolve(true);
    });
    
    // 监听模态框关闭事件
    document.getElementById('confirmModal').addEventListener('hidden.bs.modal', () => {
      resolve(false);
    }, { once: true });
    
    modal.show();
  });
},

// Loading工具函数
 showLoading(message = '正在处理中...') {
  const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
  document.getElementById('loadingText').textContent = message;
  loadingModal.show();
  return loadingModal;
},

 hideLoading() {
  const loadingModalElement = document.getElementById('loadingModal');
  const loadingModal = bootstrap.Modal.getInstance(loadingModalElement);
  if (loadingModal) {
    loadingModal.hide();
  }
},
          filpConfirm() {
            let domContainer = document.querySelectorAll('.canvas-container')
            // conso
            console.log(domContainer, 'container..')
            this.filpFlase = !this.filpFlase;
            if (this.filpFlase) {
              this.img.src = '../template/torsoBack-26c7ad9cdd648275a6ebe7a681a92b4e.png'
              this.drawImage()
              domContainer[1].style.display = 'block'
              domContainer[0].style.display = 'none'
           
               domContainer[1].querySelector('.upper-canvas').style.display = 'block'
               domContainer[0].querySelector('.upper-canvas').style.display = 'none'
            } else {
              this.img.src = '../template/torsoFront-4fdf99423cdedf8b3920751aba12066f.png'
              this.drawImage()
              domContainer[1].style.display = 'none'
              domContainer[0].style.display = 'block'
  
                domContainer[0].querySelector('.upper-canvas').style.display = 'block'
               domContainer[1].querySelector('.upper-canvas').style.display = 'none'
            }
            
          },
          updateCanvas2Background(img) {
            console.log("开始更新Canvas2背景图片...");
            if (!this.fabricCanvas2 || !img) {
              console.log("Canvas2或图片不存在，跳过更新");
              return;
            }

            // Calculate image position and dimensions
            const canvasWidth = this.fabricCanvas2.width;
            const canvasHeight = this.fabricCanvas2.height;
            const targetRatio = 1.17;
            const displayWidth = canvasWidth * 0.58;
            const displayHeight = displayWidth / targetRatio;
            const x = canvasWidth * 0.21;
            let y = (canvasHeight - canvasWidth / 1.59) / 1.87;
            if (window.innerWidth > 768) {
              y = (canvasHeight - canvasWidth / 1.59) / 1.87;
            } else {
              y = (canvasHeight - canvasWidth / 1.59) / 1.85;
            }

            // Set image properties
            img.set({
              left: x,
              top: y,
              scaleX: displayWidth / img.width,
              scaleY: displayHeight / img.height,
              selectable: false,
              evented: false,
            });

            const svgPath =
              "M 92.994 0.934 C 85.888 3.096, 78.345 9.494, 74.839 16.334 C 73.443 19.058, 62.323 79.996, 36.437 226.783 C 0.701 429.421, -0 433.654, -0 446.701 L 0 460.004 270.250 459.752 L 540.500 459.500 540.474 444.500 L 540.449 429.500 504.202 225.020 C 478.272 78.736, 467.370 19.331, 465.899 16.292 C 464.768 13.956, 461.626 10.051, 458.916 7.614 C 449.712 -0.660, 466.017 -0.011, 270.072 0.086 C 174.057 0.133, 94.372 0.515, 92.994 0.934 M 0.400 445.500 C 0.401 453.750, 0.556 456.992, 0.744 452.704 C 0.932 448.417, 0.931 441.667, 0.742 437.704 C 0.552 433.742, 0.399 437.250, 0.400 445.500"; // 示例路径

            // 创建裁剪路径
            const clipPath = new fabric.Path(svgPath, {
              left: x,
              top: y,
              scaleX: displayWidth / img.width, // 根据原始SVG尺寸调整
              scaleY: displayHeight / img.height, // 根据原始SVG尺寸调整
              absolutePositioned: true,
              fill: "transparent",
              stroke: "transparent",
            });

            // 应用裁剪路径到整个画布
            this.fabricCanvas2.clipPath = clipPath;

            // 总是添加新的背景图片（因为我们在loadCanvas2Content中已经清空了Canvas）
            console.log("添加新的背景图片到Canvas2");
            this.fabricCanvas2.add(img);

            // 确保背景图片在最底层
            this.fabricCanvas2.sendToBack(img);

            // 设置背景图片不可选择和不响应事件，确保用户只能操作前景元素
            img.set({
              selectable: false,
              evented: false,
              excludeFromExport: false, // 导出时包含背景
            });

            this.backgroundImage2 = img;

            this.fabricCanvas2.renderAll();
            console.log("Canvas2背景图片更新完成");

            console.log(
              `Canvas2 actual dimensions: ${canvasWidth}x${canvasHeight}`
            );
            console.log(`Canvas2 target ratio: 1.17:1 (${targetRatio})`);
            console.log(
              `Canvas2 display dimensions: ${displayWidth}x${displayHeight.toFixed(
                0
              )}`
            );
            console.log(`Canvas2 drawing position: (${x}, ${y.toFixed(0)})`);
          },
          getBackgroundImageUrl() {
            return (
              this.backgroundGg[this.currentBackgroundType] ||
              this.backgroundGg.default
            );
          },
          // 获取背面背景图片URL
          getBackgroundImageUrlBack() {
            return (
              this.backgroundGgBack[this.currentBackgroundType] ||
              this.backgroundGgBack.default
            );
          },
          // 获取当前活动的Canvas（根据当前显示的面）
          getCurrentCanvas() {
            return this.filpFlase ? this.fabricCanvas2Back : this.fabricCanvas2;
          },
          // 获取当前活动的背景图片对象
          getCurrentBackgroundImage() {
            return this.filpFlase ? this.backgroundImage2Back : this.backgroundImage2;
          },
          // 更新背面Canvas背景图片
          updateCanvas2BackBackground(img) {
            console.log("开始更新Canvas2Back背景图片...");
            if (!this.fabricCanvas2Back || !img) {
              console.log("Canvas2Back或图片不存在，跳过更新");
              return;
            }

            // Calculate image position and dimensions - 与正面保持一致
            const canvasWidth = this.fabricCanvas2Back.width;
            const canvasHeight = this.fabricCanvas2Back.height;
            const targetRatio = 1.17;
            const displayWidth = canvasWidth * 0.58;
            const displayHeight = displayWidth / targetRatio;
            const x = canvasWidth * 0.21;
            let y = (canvasHeight - canvasWidth / 1.59) / 1.87;
            if (window.innerWidth > 768) {
              y = (canvasHeight - canvasWidth / 1.59) / 1.87;
            } else {
              y = (canvasHeight - canvasWidth / 1.59) / 1.85;
            }

            // Set image properties
            img.set({
              left: x,
              top: y,
              scaleX: displayWidth / img.width,
              scaleY: displayHeight / img.height,
              selectable: false,
              evented: false,
            });

            // 使用与正面相同的SVG裁剪路径
            const svgPath =
              "M 92.994 0.934 C 85.888 3.096, 78.345 9.494, 74.839 16.334 C 73.443 19.058, 62.323 79.996, 36.437 226.783 C 0.701 429.421, -0 433.654, -0 446.701 L 0 460.004 270.250 459.752 L 540.500 459.500 540.474 444.500 L 540.449 429.500 504.202 225.020 C 478.272 78.736, 467.370 19.331, 465.899 16.292 C 464.768 13.956, 461.626 10.051, 458.916 7.614 C 449.712 -0.660, 466.017 -0.011, 270.072 0.086 C 174.057 0.133, 94.372 0.515, 92.994 0.934 M 0.400 445.500 C 0.401 453.750, 0.556 456.992, 0.744 452.704 C 0.932 448.417, 0.931 441.667, 0.742 437.704 C 0.552 433.742, 0.399 437.250, 0.400 445.500";

            // 创建裁剪路径
            const clipPath = new fabric.Path(svgPath, {
              left: x,
              top: y,
              scaleX: displayWidth / img.width, // 根据原始SVG尺寸调整
              scaleY: displayHeight / img.height, // 根据原始SVG尺寸调整
              absolutePositioned: true,
              fill: "transparent",
              stroke: "transparent",
            });

            // 应用裁剪路径到整个画布
            this.fabricCanvas2Back.clipPath = clipPath;

            // 总是添加新的背景图片（因为我们在loadCanvas2BackContent中已经清空了Canvas）
            console.log("添加新的背景图片到Canvas2Back");
            this.fabricCanvas2Back.add(img);

            // 确保背景图片在最底层
            this.fabricCanvas2Back.sendToBack(img);

            // 设置背景图片不可选择和不响应事件，确保用户只能操作前景元素
            img.set({
              selectable: false,
              evented: false,
              excludeFromExport: false, // 导出时包含背景
            });

            this.backgroundImage2Back = img;

            this.fabricCanvas2Back.renderAll();
            console.log("Canvas2Back背景图片更新完成");

            console.log(
              `Canvas2Back actual dimensions: ${canvasWidth}x${canvasHeight}`
            );
            console.log(`Canvas2Back target ratio: 1.17:1 (${targetRatio})`);
            console.log(
              `Canvas2Back display size: ${displayWidth.toFixed(0)}x${displayHeight.toFixed(0)}`
            );
            console.log(`Canvas2Back drawing position: (${x.toFixed(0)}, ${y.toFixed(0)})`);
          },
          loadCanvas2Content() {
            console.log("开始加载Canvas2内容...");

            // 确保Canvas2已初始化
            if (!this.fabricCanvas2) {
              console.log("Fabric Canvas2未初始化，正在初始化...");
              this.initFabricCanvas2();
            }

            // 重置背景图片变量
            this.backgroundImage2 = null;

            // Clear Canvas2
            this.fabricCanvas2.clear();
            console.log("Canvas2已清空");

            // Create background image - 支持动态配置
            const imgUrl = this.currentBackgroundType;
            console.log("正在加载背景图片:", imgUrl);

            fabric.Image.fromURL(
              imgUrl,
              (img) => {
                if (img) {
                  console.log("背景图片加载成功:", img);
                  this.updateCanvas2Background(img);
                } else {
                  console.error("背景图片加载失败");
                }
              },
              {
                // 添加错误处理
                crossOrigin: "anonymous",
              }
            );
          },
          // 加载背面Canvas内容
          loadCanvas2BackContent() {
            console.log("开始加载Canvas2Back内容...");

            // 确保背面Canvas已初始化
            if (!this.fabricCanvas2Back) {
              console.log("Fabric Canvas2Back未初始化，正在初始化...");
              this.initFabricCanvas2Back();
            }

            // 重置背面背景图片变量
            this.backgroundImage2Back = null;

            // Clear Canvas2Back
            this.fabricCanvas2Back.clear();
            console.log("Canvas2Back已清空");

            // Create background image - 使用背面背景图片配置
            const imgUrl = this.currentBackVgroundType;
            console.log("正在加载背面背景图片:", imgUrl);

            fabric.Image.fromURL(
              imgUrl,
              (img) => {
                if (img) {
                  console.log("背面背景图片加载成功:", img);
                  this.updateCanvas2BackBackground(img);
                } else {
                  console.error("背面背景图片加载失败");
                }
              },
              {
                // 添加错误处理
                crossOrigin: "anonymous",
              }
            );
          },
          initFabricCanvas2() {
            // 如果已经存在，先销毁
            if (this.fabricCanvas2) {
              this.fabricCanvas2.dispose();
              this.fabricCanvas2 = null;
            }

            // 获取设备像素比
            const pixelRatio = window.devicePixelRatio || 1;

            this.fabricCanvas2 = new fabric.Canvas("canvas2", {
              width: this.containerWidth,
              height: this.containerHeight,
              backgroundColor: "#f0f0f0",
            });

            // 为高DPI屏幕优化Fabric.js Canvas - 使用正确的方法
            if (pixelRatio > 1) {
              const canvasEl = this.fabricCanvas2.getElement();
              const ctx = canvasEl.getContext("2d");

              // 设置Canvas的实际像素尺寸
              canvasEl.width = this.containerWidth * pixelRatio;
              canvasEl.height = this.containerHeight * pixelRatio;

              // 设置Canvas的CSS显示尺寸
              canvasEl.style.width = this.containerWidth + "px";
              canvasEl.style.height = this.containerHeight + "px";

              // 缩放绘图上下文，但不改变Fabric.js的坐标系统
              ctx.scale(pixelRatio, pixelRatio);

              // 设置Fabric.js内部的设备像素比
              this.fabricCanvas2.devicePixelRatio = pixelRatio;
            }

            // 设置选择样式
            fabric.Object.prototype.set({
              transparentCorners: false,
              borderColor: "#0066ff",
              cornerColor: "#0066ff",
              cornerSize: 8,
              padding: 5,
            });

            // 监听对象选择事件
            this.fabricCanvas2.on("selection:created", function (e) {
              console.log("选中对象:", e.selected);
            });

            this.fabricCanvas2.on("selection:updated", function (e) {
              console.log("更新选择:", e.selected);
            });

            this.fabricCanvas2.on("selection:cleared", function () {
              console.log("清除选择");
            });

            // 监听对象修改事件
            this.fabricCanvas2.on("object:modified", function (e) {
              console.log("对象已修改:", e.target);
            });

            // 双击添加文字
            this.fabricCanvas2.on("mouse:dblclick", function (options) {
              const pointer = fabricCanvas2.getPointer(options.e);
              // addTextToFabricCanvas2(null, pointer.x, pointer.y);
            });

            console.log("Fabric.js Canvas2 初始化完成");
          },
          // 初始化背面Canvas
          initFabricCanvas2Back() {
            // 如果已经存在，先销毁
            if (this.fabricCanvas2Back) {
              this.fabricCanvas2Back.dispose();
              this.fabricCanvas2Back = null;
            }

            // 获取设备像素比
            const pixelRatio = window.devicePixelRatio || 1;

            this.fabricCanvas2Back = new fabric.Canvas("canvas2Back", {
              width: this.containerWidth,
              height: this.containerHeight,
              backgroundColor: "#f0f0f0",
            });

            // 为高DPI屏幕优化Fabric.js Canvas - 使用正确的方法
            if (pixelRatio > 1) {
              const canvasEl = this.fabricCanvas2Back.getElement();
              const ctx = canvasEl.getContext("2d");

              // 设置Canvas的实际像素尺寸
              canvasEl.width = this.containerWidth * pixelRatio;
              canvasEl.height = this.containerHeight * pixelRatio;

              // 设置Canvas的CSS显示尺寸
              canvasEl.style.width = this.containerWidth + "px";
              canvasEl.style.height = this.containerHeight + "px";

              // 缩放绘图上下文，但不改变Fabric.js的坐标系统
              ctx.scale(pixelRatio, pixelRatio);

              // 设置Fabric.js内部的设备像素比
              this.fabricCanvas2Back.devicePixelRatio = pixelRatio;
            }

            // 设置选择样式
            fabric.Object.prototype.set({
              transparentCorners: false,
              borderColor: "#0066ff",
              cornerColor: "#0066ff",
              cornerSize: 8,
              padding: 5,
            });

            // 监听对象选择事件
            this.fabricCanvas2Back.on("selection:created", function (e) {
              console.log("背面选中对象:", e.selected);
            });

            this.fabricCanvas2Back.on("selection:updated", function (e) {
              console.log("背面更新选择:", e.selected);
            });

            this.fabricCanvas2Back.on("selection:cleared", function () {
              console.log("背面清除选择");
            });

            // 监听对象修改事件
            this.fabricCanvas2Back.on("object:modified", function (e) {
              console.log("背面对象已修改:", e.target);
            });

            // 监听双击事件，用于添加文字
            this.fabricCanvas2Back.on("mouse:dblclick", function (options) {
              const pointer = this.getPointer(options.e);
              console.log("背面双击位置:", pointer);
              // addTextToFabricCanvas2Back(null, pointer.x, pointer.y);
            });

            console.log("Fabric.js Canvas2Back 初始化完成");
          },
          drawImage() {
            // 获取当前Canvas的尺寸
            const canvasWidth = this.containerWidth;
            const canvasHeight = this.containerHeight;

            console.log(`Canvas尺寸: ${canvasWidth}x${canvasHeight}`);

            // 按照 221:139 的比例计算图片显示尺寸
            const targetRatio = 221 / 139; // ≈ 1.59
            const displayWidth = canvasWidth; // 宽度100%
            const displayHeight = displayWidth / targetRatio; // 按比例计算高度

            // 计算居中位置
            const x = 0; // 宽度从0开始，占满100%
            const y = (canvasHeight - displayHeight) / 2; // 垂直居中

            console.log(`目标比例: 221:139 (${targetRatio.toFixed(2)})`);
            console.log(
              `显示尺寸: ${displayWidth}x${displayHeight.toFixed(0)}`
            );
            console.log(`绘制位置: (${x}, ${y.toFixed(0)})`);

            // 获取当前的绘图上下文
            const currentCtx = this.canvasContainer.getContext("2d");

            // 清除Canvas
            currentCtx.clearRect(0, 0, canvasWidth, canvasHeight);

            // 绘制图片 - 按照指定比例，宽度100%，垂直居中
            currentCtx.drawImage(this.img, x, y, displayWidth, displayHeight);
          },
          updateFabricCanvas2Size() {
            if (this.fabricCanvas2) {
              // 获取设备像素比
              const pixelRatio = window.devicePixelRatio || 1;

              this.fabricCanvas2.setDimensions({
                width: this.containerWidth,
                height: this.containerHeight,
              });

              // 为高DPI屏幕优化 - 使用正确的方法
              if (pixelRatio > 1) {
                const canvasEl = this.fabricCanvas2.getElement();
                const ctx = canvasEl.getContext("2d");

                // 设置Canvas的实际像素尺寸
                canvasEl.width = this.containerWidth * pixelRatio;
                canvasEl.height = this.containerHeight * pixelRatio;

                // 设置Canvas的CSS显示尺寸
                canvasEl.style.width = this.containerWidth + "px";
                canvasEl.style.height = this.containerHeight + "px";

                // 缩放绘图上下文，但不改变Fabric.js的坐标系统
                ctx.scale(pixelRatio, pixelRatio);

                // 设置Fabric.js内部的设备像素比
                this.fabricCanvas2.devicePixelRatio = pixelRatio;
              }

              this.fabricCanvas2.renderAll();
              // console.log(`Fabric Canvas2 尺寸已更新: ${containerWidth}x${containerHeight}, 像素比: ${pixelRatio}`);
            }
          },
          setCanvasSize() {
            const x = document.querySelector(".customer-container");
            const containerW = document.querySelector(".konvajs-content");
            const containerH = document.querySelector(
              ".customer-container-wrapper2"
            );
            if (window.innerWidth > 768) {
              containerW.style.width = x.clientHeight + "px";
              containerW.style.height = x.clientHeight + "px";
            } else {
              containerW.style.width = containerH.clientWidth + "px";
              containerW.style.height = containerH.clientWidth + "px";
            }

            // const containerH = document.querySelector('.customer-container-wrapper2');
            const canvas = document.getElementById("torsoCanvas");
            const canvas2 = document.getElementById("canvas2");
            this.containerWidth = containerW.clientWidth;
            this.containerHeight = containerW.clientHeight;

            // 获取设备像素比，用于高DPI屏幕优化
            const pixelRatio = window.devicePixelRatio || 3;

            if (containerH && containerW && canvas && canvas2) {
              // 设置Canvas的实际像素尺寸（考虑设备像素比）
              this.canvasContainer.width = this.containerWidth * pixelRatio;
              this.canvasContainer.height = this.containerHeight * pixelRatio;
              // 设置Canvas的CSS显示尺寸
              this.canvasContainer.style.width = this.containerWidth + "px";
              this.canvasContainer.style.height = this.containerHeight + "px";

              // 重新获取上下文并缩放（因为设置width/height会重置上下文）
              const newCtx = this.canvasContainer.getContext("2d");
              newCtx.scale(pixelRatio, pixelRatio);

              // 对canvas2也进行同样的处理（但Fabric.js会自己处理）
              canvas2.width = this.containerWidth * pixelRatio;
              canvas2.height = this.containerHeight * pixelRatio;
              canvas2.style.width = this.containerWidth + "px";
              canvas2.style.height = this.containerHeight + "px";

              this.drawImage();

              // 更新Fabric Canvas2尺寸和背景图片
              // this.updateFabricCanvas2Size();
            }
          },
          toLoadCanvas() {
            console.log("开始初始化正面和背面Canvas...");

            // 销毁之前的正面Canvas
            if (this.fabricCanvas2) {
              console.log("销毁之前的Fabric Canvas2");
              this.fabricCanvas2.dispose();
              this.fabricCanvas2 = null;
            }

            // 销毁之前的背面Canvas
            if (this.fabricCanvas2Back) {
              console.log("销毁之前的Fabric Canvas2Back");
              this.fabricCanvas2Back.dispose();
              this.fabricCanvas2Back = null;
            }

            // 重置背景图片变量
            this.backgroundImage2 = null;
            this.backgroundImage2Back = null;

            // Canvas1 - 原始图片
            this.canvasContainer = document.getElementById("torsoCanvas");
            const ctx = this.canvasContainer.getContext("2d");
            this.img = new Image();
            this.img.src =
              "../template/torsoFront-4fdf99423cdedf8b3920751aba12066f.png";
            this.setCanvasSize();
            window.addEventListener("resize", () => {
              if (window.innerWidth > 768) {
                this.setCanvasSize();
              }
            });
            this.img.onload = () => {
              console.log("图片加载完成");
              this.drawImage();
            };

            this.img.onerror = function (e) {
              console.error("图片加载失败，请检查路径是否正确。", e);
            };

            // 同时初始化正面和背面Canvas
            this.initFabricCanvas2();
            this.initFabricCanvas2Back();

            // 同时加载正面和背面内容
            this.loadCanvas2Content();
            this.loadCanvas2BackContent();


            console.log("正面和背面Canvas初始化完成");
          },
          selectItem(type, id, categoryIndex, itemIndex, item) {
            // 更新选中状态
            this[`${type}Category`][categoryIndex].list.forEach((item2, index) => {
              this.$set(item2, 'check', index === itemIndex);
            });

            // 记录选中的项目到 selectedItems
            this.selectedItems[type] = {
              id: id,
              name: item.alt || item.name,
              src: item.src,
              categoryIndex: categoryIndex,
              itemIndex: itemIndex
            };

            if (type === 'clothing') {
              // 服装逻辑保持不变
              if (item.src2) {
                if (this.filpFlase) {
                  this.currentBackVgroundType = item.src2
                } else {
                   this.currentBackgroundType = item.src2;
                }
                 console.log(this.currentBackgroundType, 'currentBackgroundType')
                //  return
                this.updateBackgroundOnly(); // 使用新方法
              }
              return;
            }

            let dom = document.querySelector(`[data-test="lego-figure-ca3 ${type}"]`);
            if (this.filp) {
              let url = item.src.replace("thumbnails", "display");
              dom.src = url.replace("_0.png", "_1.png");
            } else {
              let url = item.src.replace("thumbnails", "display");
              dom.src = url.replace("_1.png", "_0.png");
            }
          },
          flipFigure() {
            this.filp = !this.filp;
            Object.keys(this.selectedItems).forEach((key) => {
              console.log(key);
              let dom = document.querySelector(
                `[data-test="lego-figure-ca3 ${key}"]`
              );
               const torsoImg = document.querySelector('[data-test="minifigure-overlay-image qg"]');
               console.log(torsoImg, 'torso9')
              if (torsoImg) {
                // 保存原始图片URL作为备份
                if (!torsoImg.dataset.originalSrc) {
                  torsoImg.dataset.originalSrc = torsoImg.src;
                }
                console.log(this.torsoBackBase64, 'back')
                console.log(this.torsoFrontBase64, 'fron')
                // 设置新的base64图片
                torsoImg.src = this.filp ? this.torsoBackBase64 : this.torsoFrontBase64;
                console.log('人仔躯干图片已更新');

                // 添加自定义标记
                torsoImg.dataset.customDesign = 'true';
                torsoImg.dataset.lastUpdated = new Date().toISOString();

              } else {
                console.error('未找到躯干图片元素');
                throw new Error('未找到躯干图片元素');
              }
              // https://www.lego.com/cdn/mff/display/4275461_1.png
              if (this.filp) {
                if (dom) {
 dom.src = dom.src.replace("_0.png", "_1.png");
                }
               
              } else {
                if (dom) {
dom.src = dom.src.replace("_1.png", "_0.png");
                }
                
              }
            });
            
          },
          toQg() {
            // 使用原有的CSS类切换方式
            document
              .querySelector(".main-figure-flex")
              .classList.remove("active");
            document
              .querySelector(".main-figure-flex-v2")
              .classList.add("active");

            // 只在第一次初始化躯干
            if (!this.torsoInitialized) {
              console.log('第一次进入躯干，开始初始化...');
              this.toLoadCanvas();
              this.torsoInitialized = true;
              
            
            } 
          },
          async backToMinifigure() {
            try {
              // 检查Canvas是否有内容需要保存
              if (this.fabricCanvas2 || this.fabricCanvas2Back) {
                const userChoice = await this.showBootstrapConfirm(
                  '您有未保存的设计，是否要保存到人仔？<br><br>点击"确定"保存设计<br>点击"取消"直接返回（不保存）',
                  '保存确认'
                );
                
                if (userChoice) {
                  // 用户选择保存
                  const loading = this.showLoading('正在保存设计...');
                  try {
                    const base64Data = await this.saveCanvasToMinifigure();
                    if (base64Data) {
                      localStorage.setItem('torsoDesign', base64Data);
                      console.log('设计已保存到本地缓存');
                    }
                    this.hideLoading();
                    this.performBackToMinifigure();
                  } catch (error) {
                    this.hideLoading();
                    console.error('保存失败:', error);
                    const retryChoice = await this.showBootstrapConfirm(
                      '保存失败，是否仍要返回人仔界面？',
                      '保存失败'
                    );
                    if (retryChoice) {
                      this.performBackToMinifigure();
                    }
                  }
                  return;
                }
              }
              
              // 用户选择不保存或没有需要保存的内容，直接返回
              this.performBackToMinifigure();
              
            } catch (error) {
              console.error('返回人仔界面时出错:', error);
            }
          },
          performBackToMinifigure() {
            // 清除选中状态
            if (this.fabricCanvas2) {
              this.fabricCanvas2.discardActiveObject();
              this.fabricCanvas2.renderAll();
            }

            // 使用原有的CSS类切换方式
            document
              .querySelector(".main-figure-flex")
              .classList.add("active");
            document
              .querySelector(".main-figure-flex-v2")
              .classList.remove("active");

            // 移除所有标签按钮的active类
            const tabButtons = document.querySelectorAll(".tab-button");
            tabButtons.forEach(btn => btn.classList.remove("active"));

            // 移除所有内容区域的active类
            const tabContents = document.querySelectorAll(".tab-content");
            tabContents.forEach(content => content.classList.remove("active"));

            // 激活头部标签（默认第一个）
            const headTab = document.querySelector('.tab-button[data-tab-target="#tab-head"]');
            if (headTab) {
              headTab.classList.add("active");
            }

            // 激活头部内容区域
            const headContent = document.querySelector('#tab-head');
            if (headContent) {
              headContent.classList.add("active");
            }

            console.log('已切换回人仔预览界面');
                this.getSelectedParts()
          },
          switchTorsoTab(tabName) {
            this.activeTorsoTab = tabName;
          },
          selectClothing(clothingType) {
            this.selectedClothing = clothingType;

            // 根据选择的服装类型更新正面背景
            let clothingUrl;
            let clothingUrlBack;
            switch(clothingType) {
              case 'shirt':
                clothingUrl = './AA_Plain_Torso01M_front.png';
                clothingUrlBack = './AA_Plain_Torso01M_back.png';
                break;
              case 'jacket':
                clothingUrl = './clothing2.png';
                clothingUrlBack = './clothing2_back.png'; // 假设有对应的背面图片
                break;
              case 'sport':
                clothingUrl = './clothing3.png';
                clothingUrlBack = './clothing3_back.png'; // 假设有对应的背面图片
                break;
              default:
                clothingUrl = this.backgroundGg.default;
                clothingUrlBack = this.backgroundGgBack.default;
            }

            // 更新当前背景类型和正面背景
            this.currentBackgroundType = clothingType;
            this.backgroundGg[clothingType] = clothingUrl;
            this.backgroundGgBack[clothingType] = clothingUrlBack;

            // 同时更新正面和背面内容
            this.loadCanvas2Content();
            if (this.fabricCanvas2Back) {
              this.loadCanvas2BackContent();
            }

            console.log('Selected clothing:', clothingType);
            console.log('Front URL:', clothingUrl);
            console.log('Back URL:', clothingUrlBack);
          },
          
          selectDecoration(type, id, categoryIndex, itemIndex, item) {
            console.log('Selected decoration:', item);
            this[`${type}Category`][categoryIndex].list.forEach((item2, index) => {
              this.$set(item2, 'check', index === itemIndex);
            });

            // 获取当前活动的Canvas
            const currentCanvas = this.getCurrentCanvas();
            if (!currentCanvas) {
              console.log('当前Canvas未初始化');
              return;
            }

            console.log(`在${this.filpFlase ? '背面' : '正面'}添加装饰:`, item.alt);

            // 使用item.src2作为实际装饰图片
            fabric.Image.fromURL(item.src2, (img) => {
              if (!img) {
                console.error('装饰图片加载失败:', item.src2);
                return;
              }

              const centerX = currentCanvas.width / 2;
              const centerY = currentCanvas.height / 2;

              // 设置固定尺寸为50x50，保持图片完整比例
              const targetSize = 50;
              const scale = targetSize / Math.max(img.width, img.height); // 按最大边缩放，保持比例

              img.set({
                left: centerX - (img.width * scale) / 2,
                top: centerY - (img.height * scale) / 2,
                scaleX: scale,
                scaleY: scale,
                selectable: true,
                evented: true,
                cornerStyle: 'circle',
                transparentCorners: false,
                borderColor: '#ff6b6b',
                cornerColor: '#ff6b6b',
                cornerSize: 8,
                padding: 5
              });

              currentCanvas.add(img);
              currentCanvas.bringToFront(img);
              currentCanvas.setActiveObject(img);
              currentCanvas.renderAll();

              console.log(`装饰已添加到${this.filpFlase ? '背面' : '正面'}:`, item.alt, `原始尺寸: ${img.width}x${img.height}, 缩放比例: ${scale.toFixed(2)}`);
            }, {
              crossOrigin: 'anonymous' // 处理跨域图片
            });
          },
          
          addText() {
            if (!this.customText.trim()) return;

            // 获取当前活动的Canvas
            const currentCanvas = this.getCurrentCanvas();
            if (!currentCanvas) {
              console.log('当前Canvas未初始化');
              return;
            }

            console.log(`在${this.filpFlase ? '背面' : '正面'}添加文字:`, this.customText.trim());

            const centerX = currentCanvas.width / 2;
            const centerY = currentCanvas.height / 2;

            const textObj = new fabric.IText(this.customText.trim(), {
              left: centerX,
              top: centerY,
              fontSize: parseInt(this.textSize),
              fill: this.textColor,
              fontFamily: this.fontFamily,
              cornerStyle: 'circle',
              transparentCorners: false,
              borderColor: '#4CAF50',
              cornerColor: '#4CAF50',
              cornerSize: 8,
              padding: 5,
              originX: 'center',
              originY: 'center'
            });

            currentCanvas.add(textObj);
            currentCanvas.bringToFront(textObj);
            currentCanvas.setActiveObject(textObj);
            currentCanvas.renderAll();

            this.customText = '';
            console.log(`文字已添加到${this.filpFlase ? '背面' : '正面'}`);
          },
          
          updateTextColor() {
            const currentCanvas = this.getCurrentCanvas();
            if (!currentCanvas) return;

            const activeObject = currentCanvas.getActiveObject();
            if (activeObject && activeObject.type === 'i-text') {
              activeObject.set('fill', this.textColor);
              currentCanvas.renderAll();
              console.log(`更新了${this.filpFlase ? '背面' : '正面'}文字颜色`);
            }
          },
          
          updateTextSize() {
            const currentCanvas = this.getCurrentCanvas();
            if (!currentCanvas) return;

            const activeObject = currentCanvas.getActiveObject();
            if (activeObject && activeObject.type === 'i-text') {
              activeObject.set('fontSize', parseInt(this.textSize));
              currentCanvas.renderAll();
              console.log(`更新了${this.filpFlase ? '背面' : '正面'}文字大小`);
            }
          },

          updateFontFamily() {
            const currentCanvas = this.getCurrentCanvas();
            if (!currentCanvas) return;

            const activeObject = currentCanvas.getActiveObject();
            if (activeObject && activeObject.type === 'i-text') {
              activeObject.set('fontFamily', this.fontFamily);
              currentCanvas.renderAll();
              console.log(`更新了${this.filpFlase ? '背面' : '正面'}字体`);
            }
          },
          
          deleteSelectedText() {
            const currentCanvas = this.getCurrentCanvas();
            if (!currentCanvas) return;

            const activeObject = currentCanvas.getActiveObject();
            if (activeObject) {
              currentCanvas.remove(activeObject);
              currentCanvas.renderAll();
              console.log(`从${this.filpFlase ? '背面' : '正面'}删除了对象`);
            }
          },
          // 新增方法：只更新背景图片
          updateBackgroundOnly() {
            const currentCanvas = this.getCurrentCanvas();
            const currentBackgroundImage = this.getCurrentBackgroundImage();
            if (!currentCanvas) return;
            console.log(currentCanvas, currentBackgroundImage, 'currentBackgroundImage')
            console.log(`只更新${this.filpFlase ? '背面' : '正面'}背景图片，保留装饰和文字...`);

            // 保存所有非背景对象
            const objects = currentCanvas.getObjects().filter(obj =>
              obj !== currentBackgroundImage
            );

            // 移除旧背景
            if (currentBackgroundImage) {
              currentCanvas.remove(currentBackgroundImage);
              if (this.filpFlase) {
                this.backgroundImage2Back = null;
              } else {
                this.backgroundImage2 = null;
              }
            }

            // 加载新背景
            const imgUrl = this.filpFlase ? this.currentBackVgroundType : this.currentBackgroundType;
            fabric.Image.fromURL(imgUrl, (img) => {
              if (img) {
                if (this.filpFlase) {
                  this.updateCanvas2BackBackground(img);
                } else {
                  this.updateCanvas2Background(img);
                }

                // 重新添加所有装饰和文字对象
                objects.forEach(obj => {
                  currentCanvas.add(obj);
                  currentCanvas.bringToFront(obj);
                });

                currentCanvas.renderAll();
                console.log(`${this.filpFlase ? '背面' : '正面'}背景更新完成，装饰和文字已保留`);
              }
            }, {
              crossOrigin: "anonymous"
            });
          },
        async  uploadToSvgService(file) {
      const formData = new FormData();
      formData.append('image', file);
      console.log(formData, 'formData')
     
      try {
        const response = await axios.post('http://localhost:3000/crop-image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        
        return response.data;
      } catch (error) {
        console.error('上传到SVG服务失败:', error);
        return { success: false, error: error.message };
      }
    },
     async base64ToFile(base64Data, filename = 'canvas-image.png') {
      const response = await fetch(base64Data);
      const blob = await response.blob();
      return new File([blob], filename, { type: 'image/png' });
    },
          // 保存Canvas到人仔
          async saveCanvasToMinifigure() {
            if (!this.fabricCanvas2 || !this.fabricCanvas2Back) {
              console.error('Fabric Canvas2 未初始化');
              return null;
            }

            try {


               console.log('开始保存Canvas到人仔...');

              // 保存前清除所有选中状态，避免导出时包含选择框
              this.fabricCanvas2.discardActiveObject();
              this.fabricCanvas2.renderAll();

              // 获取Canvas内容
              const canvas = this.fabricCanvas2.getElement();

              // 生成高质量base64图片
              const base64Data = canvas.toDataURL('image/png', 1.0);
              console.log('生成base64数据:', base64Data);

              // 转换为File对象
              const file = await this.base64ToFile(base64Data, 'canvas-design.png');

              // 上传到svg.js服务进行处理
              const result = await this.uploadToSvgService(file);
              
              let finalBase64 = base64Data; // 默认使用原始base64
             
              if (result.success) {
                console.log('图片处理成功！');
                
                if (result.dataUrl) {
                
                  this.torsoFrontBase64 = result.dataUrl; // 使用处理后的数据
                   this.selectedItems.torso = {
      id: 'custom_torso',
      name: '自定义躯干',
      src: result.dataUrl,
      type: 'torso',
      isCustom: true
    };
                } else {
                 
                }
              } else {
                console.error('图片处理失败:', result.error || '未知错误');
                
                if (confirm('服务器处理失败，是否直接应用当前设计？')) {
                
                  console.log('设计已直接应用到人仔！');
                }
              }

            // 背面
              console.log('开始保存Canvas到人仔...');

              // 保存前清除所有选中状态，避免导出时包含选择框
              this.fabricCanvas2Back.discardActiveObject();
              this.fabricCanvas2Back.renderAll();

              // 获取Canvas内容
              const canvas2 = this.fabricCanvas2Back.getElement();

              // 生成高质量base64图片
              const base64BackData = canvas2.toDataURL('image/png', 1.0);
              console.log('生成base64数据:', base64BackData);

              // 转换为File对象
              const file2 = await this.base64ToFile(base64BackData, 'canvas-design.png');

              // 上传到svg.js服务进行处理
              const result2 = await this.uploadToSvgService(file2);
              
              let finalBase642 = base64BackData; // 默认使用原始base64
              console.log(result2, 'result2..')
              if (result2.success) {
                console.log('图片处理成功！');
                
                if (result2.dataUrl) {
                  
                  this.torsoBackBase64 = result2.dataUrl; // 使用处理后的数据
                
                }
              }
            } catch (error) {
              console.error('保存Canvas到人仔失败:', error);
              throw error;
            }

            this.updateMinifigureTorso(this.filp ? this.torsoBackBase64 : this.torsoFrontBase64);
          },
          // 更新人仔躯干图片
          updateMinifigureTorso(base64Data) {
            console.log('开始更新人仔躯干图片...');

            try {
              // 查找躯干图片元素
              const torsoImg = document.querySelector('[data-test="minifigure-overlay-image qg"]');

              if (torsoImg) {
                // 保存原始图片URL作为备份
                if (!torsoImg.dataset.originalSrc) {
                  torsoImg.dataset.originalSrc = torsoImg.src;
                }
                
                // 设置新的base64图片
                torsoImg.src = base64Data;
                console.log('人仔躯干图片已更新');

                // 添加自定义标记
                torsoImg.dataset.customDesign = 'true';
                torsoImg.dataset.lastUpdated = new Date().toISOString();

              } else {
                console.error('未找到躯干图片元素');
                throw new Error('未找到躯干图片元素');
              }

            } catch (error) {
              console.error('更新人仔躯干图片失败:', error);
              throw error;
            }
          },
          // 新增方法：加载缓存的设计
          loadCachedDesign() {
            const cachedDesign = localStorage.getItem('torsoDesign');
            if (cachedDesign && this.fabricCanvas2) {
              console.log('发现缓存的设计，正在加载...');
              
              // 延迟加载，确保Canvas已完全初始化
              setTimeout(() => {
                this.updateMinifigureTorso(cachedDesign);
                console.log('缓存的设计已应用到人仔');
              }, 500);
            }
          },
          // 新增方法：清除缓存的设计
          clearCachedDesign() {
            localStorage.removeItem('torsoDesign');
            console.log('缓存的设计已清除');
          },
          resetTorsoCompletely() {
            if (confirm('确定要重置躯干到原始状态吗？这将清除所有自定义内容，此操作不可撤销。')) {
              try {
                console.log('开始重置躯干...');
                
                // 1. 清除正面Fabric Canvas上的所有对象（除了背景）
                if (this.fabricCanvas2) {
                  const objects = this.fabricCanvas2.getObjects();
                  // 保留背景图片，移除其他所有对象
                  objects.forEach((obj, index) => {
                    if (index > 0) { // 第一个通常是背景图片
                      this.fabricCanvas2.remove(obj);
                    }
                  });
                  this.fabricCanvas2.renderAll();
                  console.log('正面Canvas对象已清除');
                }

                // 1.1 清除背面Fabric Canvas上的所有对象（除了背景）
                if (this.fabricCanvas2Back) {
                  const objectsBack = this.fabricCanvas2Back.getObjects();
                  // 保留背景图片，移除其他所有对象
                  objectsBack.forEach((obj, index) => {
                    if (index > 0) { // 第一个通常是背景图片
                      this.fabricCanvas2Back.remove(obj);
                    }
                  });
                  this.fabricCanvas2Back.renderAll();
                  console.log('背面Canvas对象已清除');
                }

                // 2. 重置背景到默认状态
                this.currentBackgroundType = 'https://www.lego.com/cdn/cs/set/assets/bltf6cf85cbe9964812/castle_03_front.png';
                this.loadCanvas2Content();
                if (this.fabricCanvas2Back) {
                  this.loadCanvas2BackContent();
                }
                
                // 3. 重置选择状态
                this.selectedClothing = null;
                this.selectedDecoration = null;
                
                // 4. 清除所有选中状态
                this.clothingCategory.forEach(category => {
                  category.list.forEach(item => {
                    item.check = false;
                  });
                });
                
                this.decorationCategory.forEach(category => {
                  category.list.forEach(item => {
                    item.check = false;
                  });
                });
                
                // 5. 重置文字工具状态
                this.customText = '';
                this.textColor = '#000000';
                this.fontFamily = 'Arial';
                this.textSize = 24;
                
                // 6. 清除本地缓存
                this.clearCachedDesign();
                
                // 7. 重置人仔躯干图片到原始状态
                this.resetMinifigureTorsoImage();
                
                console.log('躯干已完全重置到初始状态');
                this.showNotification('躯干已重置到初始状态', 'success');
                
              } catch (error) {
                console.error('重置躯干失败:', error);
                this.showNotification('重置失败: ' + error.message, 'error');
              }
            }
          },
          resetMinifigureTorsoImage() {
            try {
              const torsoImg = document.querySelector('[data-test="minifigure-overlay-image qg"]');
              
              if (torsoImg && torsoImg.dataset.originalSrc) {
                torsoImg.src = torsoImg.dataset.originalSrc;
                delete torsoImg.dataset.customDesign;
                delete torsoImg.dataset.lastUpdated;
                console.log('人仔躯干图片已重置到原始状态');
              } else {
                console.log('没有找到原始图片或躯干元素');
              }
            } catch (error) {
              console.error('重置躯干图片失败:', error);
            }
          },
          async confirmDesign() {
            if (!this.fabricCanvas2) {
              this.showNotification('请先进入躯干编辑模式', 'warning');
              return;
            }
            
            // 检查是否有自定义内容
            const objects = this.fabricCanvas2.getObjects();
            
            const loading = this.showLoading('正在确认设计...');
            try {
              const base64Data = await this.saveCanvasToMinifigure();
              if (base64Data) {
                localStorage.setItem('torsoDesign', base64Data);
                
                // 确保躯干被添加到已选部件
                this.selectedItems.torso = {
                  id: 'custom_torso',
                  name: '自定义躯干',
                  src: this.torsoFrontBase64,
                  type: 'torso',
                  isCustom: true
                };
                
                console.log('设计已确认并保存');
                this.showNotification('设计已确认并应用到人仔！', 'success');
                
                setTimeout(() => {
                  this.performBackToMinifigure();
                }, 1000);
              }
            } catch (error) {
              console.error('确认设计失败:', error);
              this.showNotification('确认失败: ' + error.message, 'error');
            } finally {
              this.hideLoading();
            }
          },
          showNotification(message, type = 'info') {
            // 简单的通知实现，你可以根据需要使用更复杂的通知组件
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
              position: fixed;
              top: 20px;
              right: 20px;
              padding: 12px 20px;
              border-radius: 6px;
              color: white;
              font-weight: 500;
              z-index: 9999;
              transition: all 0.3s ease;
              ${type === 'success' ? 'background: #28a745;' : ''}
              ${type === 'error' ? 'background: #dc3545;' : ''}
              ${type === 'warning' ? 'background: #ffc107; color: #212529;' : ''}
              ${type === 'info' ? 'background: #17a2b8;' : ''}
            `;
            
            document.body.appendChild(notification);
            
            // 3秒后自动移除
            setTimeout(() => {
              notification.style.opacity = '0';
              setTimeout(() => {
                if (notification.parentNode) {
                  notification.parentNode.removeChild(notification);
                }
              }, 300);
            }, 3000);
          },
          
          getSelectedParts() {
            console.log("hhahaahah")
            const parts = [];
            
            // 头部
            if (this.selectedItems.head) {
              parts.push({
                type: 'head',
                name: this.selectedItems.head.name || '头部',
                iconStyle: { backgroundColor: '#ffcc00' },
                thumbnail: this.selectedItems.head.src
              });
            }
            
            // 发型
            if (this.selectedItems.hair) {
              parts.push({
                type: 'hair',
                name: this.selectedItems.hair.name || '发型',
                iconStyle: { backgroundColor: '#8b4513' },
                thumbnail: this.selectedItems.hair.src
              });
            }
            
            // 表情
            if (this.selectedItems.expression) {
              parts.push({
                type: 'expression',
                name: this.selectedItems.expression.name || '表情',
                icon: 'bi bi-emoji-smile',
                iconStyle: { backgroundColor: '#ff6b6b' }
              });
            }
            
            // 自定义躯干 - 只有当用户自定义了躯干才显示
            if (this.selectedItems.torso && this.selectedItems.torso.isCustom) {
              parts.push({
                type: 'torso',
                name: '自定义躯干',
                iconStyle: { backgroundColor: '#28a745' },
                thumbnail: this.torsoFrontBase64, // 使用正面base64图片
                isCustom: true
              });
            }
            
            // 腿部
            if (this.selectedItems.leg) {
              parts.push({
                type: 'leg',
                name: this.selectedItems.leg.name || '腿部',
                iconStyle: { backgroundColor: '#0066cc' },
                thumbnail: this.selectedItems.leg.src
              });
            }
            
            // 细节
            if (this.selectedItems.details) {
              parts.push({
                type: 'details',
                name: this.selectedItems.details.name || '装饰细节',
                icon: 'bi bi-star',
                iconStyle: { backgroundColor: '#6c757d' }
              });
            }
            
            // 装饰
            if (this.selectedItems.decoration) {
              parts.push({
                type: 'decoration',
                name: this.selectedItems.decoration.name || '装饰',
                icon: 'bi bi-gem',
                iconStyle: { backgroundColor: '#6f42c1' }
              });
            }
            
            return parts;
          },
          findSelectedItem(type) {
            const categories = this[`${type}Category`];
            if (!categories) return null;
            
            for (let category of categories) {
              const selectedItem = category.list.find(item => item.check);
              if (selectedItem) return selectedItem;
            }
            return null;
          },
          removePart(type) {
            // 移除选中状态
            this.selectedItems[type] = null;
            
            // 更新对应分类的选中状态
            const categories = this[`${type}Category`];
            if (categories) {
              categories.forEach(category => {
                category.list.forEach(item => {
                  this.$set(item, 'check', false);
                });
              });
            }
            
            // 重置对应的预览图片
            this.resetPartPreview(type);
          },
          resetPartPreview(type) {
            const selectors = {
              head: '[data-test="minifigure-brick-image head"]',
              hair: '[data-test="minifigure-brick-image front-headwear"]',
              expression: '[data-test="minifigure-brick-image front-leg"]',
              leg: '[data-test="minifigure-brick-image front-leg"]',
              details: '[data-test="minifigure-brick-image front-acc"]'
            };
            
            const element = document.querySelector(selectors[type]);
            if (element) {
              // 恢复默认图片或清空
              element.src = '';
            }
          },

          // 添加数据加载方法
          async loadAllData() {
            try {
              await this.dataLoader.loadAllCategoryData();
              console.log('所有数据加载完成');
            } catch (error) {
              console.error('数据加载失败:', error);
            }
          },

          // 重新加载指定大类数据
          async reloadCategoryData(mainCategory) {
            try {
              await this.dataLoader.reloadCategoryData(mainCategory);
              console.log(`${mainCategory}数据重新加载完成`);
            } catch (error) {
              console.error(`${mainCategory}数据重新加载失败:`, error);
            }
          }
        },
        mounted() {
          // 初始化数据加载器
          this.dataLoader = new LegoDataLoader(this);

          // 加载所有分类数据
          this.loadAllData();
        },
      });
    </script>
  </body>
</html>
