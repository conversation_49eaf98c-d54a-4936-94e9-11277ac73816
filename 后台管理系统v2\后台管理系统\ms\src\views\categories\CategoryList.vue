<template>
  <div class="category-list">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>分类管理</h2>
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        添加分类
      </el-button>
    </div>

    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="分类名称">
          <el-input v-model="searchForm.category" placeholder="请输入分类名称" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="大类">
          <el-select v-model="searchForm.main_category" placeholder="请选择大类" clearable style="width: 150px">
            <el-option label="头部" value="头部" />
            <el-option label="发型" value="发型" />
            <el-option label="躯干" value="躯干" />
            <el-option label="腿部" value="腿部" />
            <el-option label="配件" value="配件" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="fetchCategories">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table :data="categories" v-loading="loading" stripe border>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="category" label="分类名称" min-width="150" />
        <el-table-column prop="main_category" label="大类" width="100">
          <template #default="{ row }">
            <el-tag :type="getMainCategoryType(row.main_category)">
              {{ row.main_category }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="内容描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="子项" width="120">
          <template #default="{ row }">
            <el-button type="info" size="small" @click="showItemsDialog(row)">
              <el-icon><Grid /></el-icon>
              管理子项
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editCategory(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteCategory(row)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form :model="categoryForm" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="分类名称" prop="category">
          <el-input v-model="categoryForm.category" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="大类" prop="main_category">
          <el-select v-model="categoryForm.main_category" placeholder="请选择大类" style="width: 100%">
            <el-option label="头部" value="头部" />
            <el-option label="发型" value="发型" />
            <el-option label="躯干" value="躯干" />
            <el-option label="腿部" value="腿部" />
            <el-option label="配件" value="配件" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容描述" prop="content">
          <el-input
            v-model="categoryForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入内容描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
      </template>
    </el-dialog>

    <!-- 子项管理对话框 -->
    <el-dialog
      title="子项管理"
      v-model="itemsDialogVisible"
      width="80%"
      @close="resetItemsDialog"
    >
      <div class="items-header">
        <h3>{{ currentCategory?.category }} - 子项列表</h3>
        <el-button type="primary" @click="showAddItemDialog">
          <el-icon><Plus /></el-icon>
          添加子项
        </el-button>
      </div>

      <el-table :data="items" v-loading="itemsLoading" stripe border>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="缩略图" width="100">
          <template #default="{ row }">
            <el-image
              
              :src="getImageUrl(row.thumbnail)"
              :preview-src-list="[getImageUrl(row.thumbnail)]"
              style="width: 60px; height: 60px"
              fit="cover"
              @click="previewImage(getImageUrl(row.thumbnail))"
            />
          </template>
        </el-table-column>
        <el-table-column label="前置图" width="100">
          <template #default="{ row }">
            <el-image
              :src="getImageUrl(row.front_image)"
              :preview-src-list="[getImageUrl(row.front_image)]"
              style="width: 60px; height: 60px"
              fit="cover"
              @click="previewImage(getImageUrl(row.front_image))"
            />
          </template>
        </el-table-column>
        <el-table-column label="后置图" width="100">
          <template #default="{ row }">
            <el-image
            
              :src="getImageUrl(row.back_image)"
              :preview-src-list="[getImageUrl(row.back_image)]"
              style="width: 60px; height: 60px"
              fit="cover"
              @click="previewImage(getImageUrl(row.back_image))"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" min-width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
              {{ row.status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editItem(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="deleteItem(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 添加/编辑子项对话框 -->
    <el-dialog
    append-to-body	
      :title="itemDialogTitle"
      v-model="itemDialogVisible"
      width="600px"
      @close="resetItemForm"
    >
      <el-form :model="itemForm" :rules="itemRules" ref="itemFormRef" label-width="100px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="itemForm.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="缩略图" prop="thumbnail">
          <el-upload
            :auto-upload="false"
            :on-change="handleThumbnailChange"
            :show-file-list="false"
            accept="image/*"
          >
            <el-button type="primary">选择缩略图</el-button>
          </el-upload>
          <div v-if="thumbnailPreview" class="image-preview">
            <el-image :src="thumbnailPreview" style="width: 100px; height: 100px" fit="cover" />
          </div>
        </el-form-item>
        <el-form-item label="前置图" prop="front_image">
          <el-upload
            :auto-upload="false"
            :on-change="handleFrontImageChange"
            :show-file-list="false"
            accept="image/*"
          >
            <el-button type="primary">选择前置图</el-button>
          </el-upload>
          <div v-if="frontImagePreview" class="image-preview">
            <el-image :src="frontImagePreview" style="width: 100px; height: 100px" fit="cover" />
          </div>
        </el-form-item>
        <el-form-item label="后置图" prop="back_image">
          <el-upload
            :auto-upload="false"
            :on-change="handleBackImageChange"
            :show-file-list="false"
            accept="image/*"
          >
            <el-button type="primary">选择后置图</el-button>
          </el-upload>
          <div v-if="backImagePreview" class="image-preview">
            <el-image :src="backImagePreview" style="width: 100px; height: 100px" fit="cover" />
          </div>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="itemForm.status" placeholder="请选择状态">
            <el-option label="启用" value="active" />
            <el-option label="禁用" value="inactive" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容描述" prop="content">
          <el-input
            v-model="itemForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入内容描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="itemDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitItemForm" :loading="itemSubmitting">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { Plus, Search, Edit, Delete, Grid } from '@element-plus/icons-vue'
import { categoryAPI, itemAPI } from '@/utils/api'

// 使用环境变量获取
const API_BASE_URL = 'http://localhost:3000'

// 创建图片URL的辅助函数
const getImageUrl = (imagePath) => {
  if (!imagePath) return ''
  // 如果路径已经包含http，直接返回
  if (imagePath.startsWith('http')) return imagePath
  // 否则拼接基础URL
  return `${API_BASE_URL}/${imagePath.replace(/^\/+/, '')}`
}

// 响应式数据
const categories = ref([])
const loading = ref(false)
const dialogVisible = ref(false)
const submitting = ref(false)
const isEdit = ref(false)
const formRef = ref()

// 子项相关数据
const items = ref([])
const itemsLoading = ref(false)
const itemsDialogVisible = ref(false)
const itemDialogVisible = ref(false)
const itemSubmitting = ref(false)
const isItemEdit = ref(false)
const itemFormRef = ref()
const currentCategory = ref(null)

// 图片相关
const thumbnailFile = ref(null)
const frontImageFile = ref(null)
const backImageFile = ref(null)
const thumbnailPreview = ref('')
const frontImagePreview = ref('')
const backImagePreview = ref('')

// 搜索表单
const searchForm = reactive({
  category: '',
  main_category: ''
})

// 分类表单
const categoryForm = reactive({
  id: '',
  category: '',
  main_category: '',
  content: ''
})

// 子项表单
const itemForm = reactive({
  id: '',
  category_id: '',
  name: '',
  status: 'active',
  content: ''
})

// 表单验证规则
const rules = {
  category: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  main_category: [
    { required: true, message: '请选择大类', trigger: 'change' }
  ]
}

const itemRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ]
}

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑分类' : '添加分类')
const itemDialogTitle = computed(() => isItemEdit.value ? '编辑子项' : '添加子项')

// 获取分类列表
const fetchCategories = async () => {
  loading.value = true
  try {
    const response = await categoryAPI.getAll(searchForm)
    categories.value = response.data.data || response.data
  } catch (error) {
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 显示添加对话框
const showAddDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

// 编辑分类
const editCategory = (row) => {
  isEdit.value = true
  categoryForm.id = row.id
  categoryForm.category = row.category
  categoryForm.main_category = row.main_category
  categoryForm.content = row.content
  dialogVisible.value = true
}

// 删除分类
const deleteCategory = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个分类吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await categoryAPI.delete(row.id)
    ElMessage.success('删除成功')
    fetchCategories()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (isEdit.value) {
      await categoryAPI.update(categoryForm.id, categoryForm)
      ElMessage.success('更新成功')
    } else {
      await categoryAPI.create(categoryForm)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    fetchCategories()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  categoryForm.id = ''
  categoryForm.category = ''
  categoryForm.main_category = ''
  categoryForm.content = ''
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 重置搜索
const resetSearch = () => {
  searchForm.category = ''
  searchForm.main_category = ''
  fetchCategories()
}

// 显示子项管理对话框
const showItemsDialog = async (category) => {
  currentCategory.value = category
  itemsDialogVisible.value = true
  await fetchItems(category.id)
}

// 获取子项列表
const fetchItems = async (categoryId) => {
  itemsLoading.value = true
  try {
    const response = await itemAPI.getByCategory(categoryId)
    items.value = response.data.data || response.data
  } catch (error) {
    ElMessage.error('获取子项列表失败')
  } finally {
    itemsLoading.value = false
  }
}

// 显示添加子项对话框
const showAddItemDialog = () => {
  isItemEdit.value = false
  itemForm.category_id = currentCategory.value.id
  itemDialogVisible.value = true
}

// 编辑子项 - 使用辅助函数
const editItem = (row) => {
  isItemEdit.value = true
  itemForm.id = row.id
  itemForm.category_id = row.category_id
  itemForm.name = row.name
  itemForm.status = row.status
  itemForm.content = row.content
  
  // 图片回显 - 使用辅助函数
  thumbnailPreview.value = getImageUrl(row.thumbnail)
  frontImagePreview.value = getImageUrl(row.front_image)
  backImagePreview.value = getImageUrl(row.back_image)
  
  itemDialogVisible.value = true
}

// 删除子项 - 添加二次确认和图片删除
const deleteItem = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除子项 "${row.name}" 吗？删除后将无法恢复，相关图片也会被删除。`, 
      '删除确认', 
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )
    
    // 显示删除进度
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在删除...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    
    try {
      await itemAPI.delete(row.id)
      ElMessage.success('删除成功')
      fetchItems(currentCategory.value.id)
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败，请重试')
    } finally {
      loadingInstance.close()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除操作失败')
    }
  }
}

// 处理图片上传
const handleThumbnailChange = (file) => {
  thumbnailFile.value = file.raw
  thumbnailPreview.value = URL.createObjectURL(file.raw)
}

const handleFrontImageChange = (file) => {
  frontImageFile.value = file.raw
  frontImagePreview.value = URL.createObjectURL(file.raw)
}

const handleBackImageChange = (file) => {
  backImageFile.value = file.raw
  backImagePreview.value = URL.createObjectURL(file.raw)
}

// 提交子项表单
const submitItemForm = async () => {
  if (!itemFormRef.value) return
  
  try {
    await itemFormRef.value.validate()
    
    if (!isItemEdit.value && (!thumbnailFile.value || !frontImageFile.value || !backImageFile.value)) {
      ElMessage.error('请上传所有必需的图片')
      return
    }
    
    itemSubmitting.value = true
    
    const formData = new FormData()
    formData.append('category_id', itemForm.category_id)
    formData.append('name', itemForm.name)
    formData.append('status', itemForm.status)
    formData.append('content', itemForm.content)
    
    if (thumbnailFile.value) {
      formData.append('thumbnail', thumbnailFile.value)
    }
    if (frontImageFile.value) {
      formData.append('front_image', frontImageFile.value)
    }
    if (backImageFile.value) {
      formData.append('back_image', backImageFile.value)
    }
    
    if (isItemEdit.value) {
      await itemAPI.update(itemForm.id, formData)
      ElMessage.success('更新成功')
    } else {
      await itemAPI.create(formData)
      ElMessage.success('创建成功')
    }
    
    itemDialogVisible.value = false
    fetchItems(currentCategory.value.id)
  } catch (error) {
    ElMessage.error(isItemEdit.value ? '更新失败' : '创建失败')
  } finally {
    itemSubmitting.value = false
  }
}

// 重置子项表单 - 清空图片预览
const resetItemForm = () => {
  itemForm.id = ''
  itemForm.category_id = ''
  itemForm.name = ''
  itemForm.status = 'active'
  itemForm.content = ''
  
  // 清空图片文件和预览
  thumbnailFile.value = null
  frontImageFile.value = null
  backImageFile.value = null
  thumbnailPreview.value = ''
  frontImagePreview.value = ''
  backImagePreview.value = ''
  
  if (itemFormRef.value) {
    itemFormRef.value.resetFields()
  }
}

// 重置子项对话框
const resetItemsDialog = () => {
  items.value = []
  currentCategory.value = null
}

// 格式化日期
const formatDate = (date) => {
  return new Date(date).toLocaleString()
}

// 获取大类标签类型
const getMainCategoryType = (mainCategory) => {
  const typeMap = {
    '头部': 'primary',
    '发型': 'success',
    '躯干': 'warning',
    '腿部': 'danger',
    '配件': 'info'
  }
  return typeMap[mainCategory] || 'default'
}

// 图片预览功能
const previewImage = (src) => {
  // Element Plus 的图片预览会自动处理
  console.log('预览图片:', src)
}

// 页面加载时获取数据
onMounted(() => {
  fetchCategories()
})
</script>

<style scoped>
.category-list {
  padding: 24px;
  height: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8eaec;
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 500;
}

.search-area {
  background-color: #fafafa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.search-form {
  margin: 0;
}

.table-container {
  background-color: #fff;
  border-radius: 6px;
  overflow: hidden;
}

.items-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.items-header h3 {
  margin: 0;
  color: #333;
}

.image-preview-container {
  text-align: center;
}

.image-preview {
  margin-top: 8px;
}

.el-imag
e {
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.el-image:hover {
  border-color: #409eff;
}
</style>











