// API配置
const API_BASE_URL = 'http://127.0.0.1:3000/api';

// API工具类
class LegoAPI {
  // 根据大类获取分类列表
  static async getCategoriesByMainCategory(mainCategory) {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories`, {
        params: { main_category: mainCategory }
      });
      return response.data;
    } catch (error) {
      console.error('获取分类失败:', error);
      throw error;
    }
  }

  // 根据分类ID获取子项列表
  static async getItemsByCategory(categoryId) {
    try {
      const response = await axios.get(`${API_BASE_URL}/items/category/${categoryId}`);
      return response.data;
    } catch (error) {
      console.error('获取子项失败:', error);
      throw error;
    }
  }

  // 获取所有分类
  static async getAllCategories() {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories`);
      return response.data;
    } catch (error) {
      console.error('获取所有分类失败:', error);
      throw error;
    }
  }

  // 图片URL处理
  static getImageUrl(imagePath) {
    if (!imagePath) return '';
    // 如果路径已经包含http，直接返回
    if (imagePath.startsWith('http')) return imagePath;
    // 否则拼接基础URL
    return `http://127.0.0.1:3000/${imagePath.replace(/^\/+/, '')}`;
  }
}

// 数据转换工具类
class DataTransformer {
  // 将后端分类数据转换为前端需要的格式
  static transformCategoryData(categories, items) {
    return categories.map(category => ({
      name: category.category,
      list: items
        .filter(item => item.category_id === category.id)
        .map(item => ({
          id: item.id,
          src: LegoAPI.getImageUrl(item.thumbnail),
          src2: LegoAPI.getImageUrl(item.front_image),
          alt: item.name,
          check: false,
          dataClass: `item-${item.id}`
        }))
    }));
  }

  // 根据大类名称获取对应的Vue数据属性名
  static getVueDataProperty(mainCategory) {
    const propertyMap = {
      '头部': 'headCategory',
      '发型': 'hairCategory', 
      '躯干': 'clothingCategory',
      '腿部': 'legCategory',
      '配件': 'detailsCategory'
    };
    return propertyMap[mainCategory];
  }
}

// 乐高页面数据加载器
class LegoDataLoader {
  constructor(vueInstance) {
    this.vue = vueInstance;
  }

  // 加载指定大类的数据
  async loadCategoryData(mainCategory) {
    try {
      console.log(`开始加载${mainCategory}数据...`);
      
      // 获取该大类下的所有分类
      const categoriesResponse = await LegoAPI.getCategoriesByMainCategory(mainCategory);
      const categories = categoriesResponse.data || [];
      
      if (categories.length === 0) {
        console.log(`${mainCategory}暂无分类数据`);
        return;
      }

      // 获取所有分类的子项
      const allItems = [];
      for (const category of categories) {
        try {
          const itemsResponse = await LegoAPI.getItemsByCategory(category.id);
          const items = itemsResponse.data || [];
          allItems.push(...items);
        } catch (error) {
          console.warn(`获取分类${category.id}的子项失败:`, error);
        }
      }

      // 转换数据格式
      const transformedData = DataTransformer.transformCategoryData(categories, allItems);
      
      // 更新Vue实例数据
      const vueProperty = DataTransformer.getVueDataProperty(mainCategory);
      if (vueProperty && this.vue[vueProperty]) {
        this.vue[vueProperty] = transformedData;
        console.log(`${mainCategory}数据加载完成:`, transformedData);
      } else {
        console.warn(`未找到对应的Vue属性: ${vueProperty}`);
      }

    } catch (error) {
      console.error(`加载${mainCategory}数据失败:`, error);
    }
  }

  // 加载所有大类数据
  async loadAllCategoryData() {
    const mainCategories = ['头部', '发型', '躯干', '腿部', '配件'];
    
    for (const mainCategory of mainCategories) {
      await this.loadCategoryData(mainCategory);
    }
    
    console.log('所有分类数据加载完成');
  }

  // 重新加载指定大类数据
  async reloadCategoryData(mainCategory) {
    await this.loadCategoryData(mainCategory);
  }
}

// 导出到全局
window.LegoAPI = LegoAPI;
window.DataTransformer = DataTransformer;
window.LegoDataLoader = LegoDataLoader;
