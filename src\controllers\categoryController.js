const Category = require('../models/categoryModel');

// 获取所有分类
exports.getAllCategories = async (req, res) => {
  try {
    const { category, main_category } = req.query;

    let categories;

    // 如果有大类筛选条件
    if (main_category && main_category.trim() !== '') {
      categories = await Category.findByMainCategory(main_category);
    }
    // 如果有搜索条件，使用搜索方法
    else if (category && category.trim() !== '') {
      categories = await Category.search(category);
    } else {
      categories = await Category.findAll();
    }

    res.status(200).json({
      success: true,
      count: categories.length,
      data: categories
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取分类列表失败',
      error: error.message
    });
  }
};

// 获取单个分类
exports.getCategoryById = async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: '未找到该分类'
      });
    }
    
    res.status(200).json({
      success: true,
      data: category
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取分类信息失败',
      error: error.message
    });
  }
};

// 创建分类
exports.createCategory = async (req, res) => {
  try {
    const { category, content } = req.body;
    
    if (!category) {
      return res.status(400).json({
        success: false,
        message: '请提供分类名称'
      });
    }
    
    const categoryId = await Category.create({ category, content });
    const newCategory = await Category.findById(categoryId);
    
    res.status(201).json({
      success: true,
      message: '分类创建成功',
      data: newCategory
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建分类失败',
      error: error.message
    });
  }
};

// 更新分类
exports.updateCategory = async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: '未找到该分类'
      });
    }
    
    const updated = await Category.update(req.params.id, req.body);
    
    if (updated) {
      const updatedCategory = await Category.findById(req.params.id);
      res.status(200).json({
        success: true,
        message: '分类更新成功',
        data: updatedCategory
      });
    } else {
      res.status(500).json({
        success: false,
        message: '分类更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新分类失败',
      error: error.message
    });
  }
};

// 删除分类
exports.deleteCategory = async (req, res) => {
  try {
    const category = await Category.findById(req.params.id);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: '未找到该分类'
      });
    }
    
    const deleted = await Category.delete(req.params.id);
    
    if (deleted) {
      res.status(200).json({
        success: true,
        message: '分类删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '分类删除失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除分类失败',
      error: error.message
    });
  }
};